{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧬 KAVYA Genomics Analysis Pipeline\n", "\n", "This notebook demonstrates the complete genomics analysis pipeline for antimicrobial resistance detection in the KAVYA system.\n", "\n", "## 🎯 Objectives\n", "- Process raw genomic sequencing data (FASTQ)\n", "- Perform genome assembly and quality control\n", "- Detect antimicrobial resistance genes\n", "- Predict novel resistance mechanisms\n", "- Generate comprehensive resistance profiles\n", "\n", "## 📊 Pipeline Overview\n", "1. **Data Preprocessing**: Quality control and trimming\n", "2. **Genome Assembly**: De novo assembly using SPAdes\n", "3. **Gene Annotation**: Prokka for gene prediction\n", "4. **Resistance Detection**: AMRFinderPlus and custom models\n", "5. **Phylogenetic Analysis**: Evolutionary relationships\n", "6. **Visualization**: Interactive plots and reports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Import required libraries\n", "import os\n", "import sys\n", "import subprocess\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "from Bio import SeqIO, Phylo, AlignIO\n", "from Bio.Seq import Seq\n", "from Bio.SeqRecord import SeqRecord\n", "from Bio.Align.Applications import MuscleCommandline\n", "from Bio.Phylo.TreeConstruction import DistanceCalculator, DistanceTreeConstructor\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up paths\n", "sys.path.append('../')\n", "from ai_models.genomics.resistance_predictor import ResistancePredictor, DNATokenizer\n", "\n", "# Configure plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"🧬 KAVYA Genomics Analysis Pipeline Initialized\")\n", "print(f\"Working directory: {os.getcwd()}\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📁 Data Setup and Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration\n", "config = {\n", "    'data_dir': '../data/genomics/',\n", "    'output_dir': '../data/results/',\n", "    'models_dir': '../models/',\n", "    'threads': 8,\n", "    'min_contig_length': 500,\n", "    'coverage_threshold': 10\n", "}\n", "\n", "# Create directories if they don't exist\n", "for directory in config.values():\n", "    if isinstance(directory, str) and directory.endswith('/'):\n", "        os.makedirs(directory, exist_ok=True)\n", "\n", "# Sample data information\n", "samples = {\n", "    'MRSA_001': {\n", "        'organism': 'Staphylococcus aureus',\n", "        'source': 'Blood culture',\n", "        'resistance_profile': 'MRSA',\n", "        'fastq_r1': 'MRSA_001_R1.fastq.gz',\n", "        'fastq_r2': 'MRSA_001_R2.fastq.gz'\n", "    },\n", "    'ESBL_002': {\n", "        'organism': 'Escherichia coli',\n", "        'source': 'Urine culture',\n", "        'resistance_profile': 'ESBL',\n", "        'fastq_r1': 'ESBL_002_R1.fastq.gz',\n", "        'fastq_r2': 'ESBL_002_R2.fastq.gz'\n", "    },\n", "    'VRE_003': {\n", "        'organism': 'Enterococcus faecium',\n", "        'source': 'Wound swab',\n", "        'resistance_profile': 'VRE',\n", "        'fastq_r1': 'VRE_003_R1.fastq.gz',\n", "        'fastq_r2': 'VRE_003_R2.fastq.gz'\n", "    }\n", "}\n", "\n", "print(f\"📊 Configured analysis for {len(samples)} samples\")\n", "for sample_id, info in samples.items():\n", "    print(f\"  - {sample_id}: {info['organism']} ({info['resistance_profile']})\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Quality Control and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def run_fastqc(sample_id, fastq_files, output_dir):\n", "    \"\"\"Run FastQC quality control analysis\"\"\"\n", "    print(f\"Running FastQC for {sample_id}...\")\n", "    \n", "    fastqc_dir = os.path.join(output_dir, sample_id, 'fastqc')\n", "    os.makedirs(fastqc_dir, exist_ok=True)\n", "    \n", "    for fastq_file in fastq_files:\n", "        if os.path.exists(os.path.join(config['data_dir'], fastq_file)):\n", "            cmd = f\"fastqc {os.path.join(config['data_dir'], fastq_file)} -o {fastqc_dir}\"\n", "            print(f\"  Command: {cmd}\")\n", "            # subprocess.run(cmd, shell=True, check=True)\n", "        else:\n", "            print(f\"  ⚠️ File not found: {fastq_file} (using simulated data)\")\n", "    \n", "    return fastqc_dir\n", "\n", "def run_trimmomatic(sample_id, fastq_r1, fastq_r2, output_dir):\n", "    \"\"\"Run Trimmomatic for adapter removal and quality trimming\"\"\"\n", "    print(f\"Running Trimmomatic for {sample_id}...\")\n", "    \n", "    trim_dir = os.path.join(output_dir, sample_id, 'trimmed')\n", "    os.makedirs(trim_dir, exist_ok=True)\n", "    \n", "    # Output files\n", "    r1_paired = os.path.join(trim_dir, f\"{sample_id}_R1_paired.fastq.gz\")\n", "    r1_unpaired = os.path.join(trim_dir, f\"{sample_id}_R1_unpaired.fastq.gz\")\n", "    r2_paired = os.path.join(trim_dir, f\"{sample_id}_R2_paired.fastq.gz\")\n", "    r2_unpaired = os.path.join(trim_dir, f\"{sample_id}_R2_unpaired.fastq.gz\")\n", "    \n", "    # Trimmomatic command\n", "    cmd = f\"\"\"trimmomatic PE -threads {config['threads']} \\\n", "        {os.path.join(config['data_dir'], fastq_r1)} \\\n", "        {os.path.join(config['data_dir'], fastq_r2)} \\\n", "        {r1_paired} {r1_unpaired} {r2_paired} {r2_unpaired} \\\n", "        ILLUMINACLIP:adapters.fa:2:30:10 \\\n", "        LEADING:3 TRAILING:3 SLIDINGWINDOW:4:15 MINLEN:36\"\"\"\n", "    \n", "    print(f\"  Command: {cmd}\")\n", "    # subprocess.run(cmd, shell=True, check=True)\n", "    \n", "    # Simulate output files for demo\n", "    for output_file in [r1_paired, r2_paired]:\n", "        with open(output_file.replace('.gz', ''), 'w') as f:\n", "            f.write(\"@simulated_read\\nATCGATCGATCG\\n+\\nIIIIIIIIIIII\\n\")\n", "    \n", "    return r1_paired, r2_paired\n", "\n", "# Run quality control for all samples\n", "qc_results = {}\n", "for sample_id, info in samples.items():\n", "    print(f\"\\n🔍 Processing {sample_id}...\")\n", "    \n", "    # FastQC\n", "    fastqc_dir = run_fastqc(sample_id, [info['fastq_r1'], info['fastq_r2']], config['output_dir'])\n", "    \n", "    # Trimmomatic\n", "    r1_trimmed, r2_trimmed = run_trimmomatic(sample_id, info['fastq_r1'], info['fastq_r2'], config['output_dir'])\n", "    \n", "    qc_results[sample_id] = {\n", "        'fastqc_dir': fastqc_dir,\n", "        'r1_trimmed': r1_trimmed,\n", "        'r2_trimmed': r2_trimmed\n", "    }\n", "\n", "print(\"\\n✅ Quality control completed for all samples\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧬 Genome Assembly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def run_spades_assembly(sample_id, r1_trimmed, r2_trimmed, output_dir):\n", "    \"\"\"Run SPAdes genome assembly\"\"\"\n", "    print(f\"Running SPAdes assembly for {sample_id}...\")\n", "    \n", "    assembly_dir = os.path.join(output_dir, sample_id, 'assembly')\n", "    os.makedirs(assembly_dir, exist_ok=True)\n", "    \n", "    # SPAdes command\n", "    cmd = f\"\"\"spades.py --pe1-1 {r1_trimmed} --pe1-2 {r2_trimmed} \\\n", "        -o {assembly_dir} --threads {config['threads']} --careful\"\"\"\n", "    \n", "    print(f\"  Command: {cmd}\")\n", "    # subprocess.run(cmd, shell=True, check=True)\n", "    \n", "    # Simulate assembly output\n", "    contigs_file = os.path.join(assembly_dir, 'contigs.fasta')\n", "    scaffolds_file = os.path.join(assembly_dir, 'scaffolds.fasta')\n", "    \n", "    # Create simulated assembly\n", "    simulated_contigs = [\n", "        SeqRecord(Seq(\"ATCGATCGATCG\" * 100), id=f\"contig_1\", description=\"length=1200\"),\n", "        SeqRecord(Seq(\"GCTAGCTAGCTA\" * 80), id=f\"contig_2\", description=\"length=960\"),\n", "        SeqRecord(Seq(\"TTAATTAATTAA\" * 60), id=f\"contig_3\", description=\"length=720\")\n", "    ]\n", "    \n", "    SeqIO.write(simulated_contigs, contigs_file, \"fasta\")\n", "    SeqIO.write(simulated_contigs, scaffolds_file, \"fasta\")\n", "    \n", "    return contigs_file, scaffolds_file\n", "\n", "def analyze_assembly_quality(contigs_file):\n", "    \"\"\"Analyze assembly quality metrics\"\"\"\n", "    contigs = list(SeqIO.parse(contigs_file, \"fasta\"))\n", "    \n", "    lengths = [len(contig.seq) for contig in contigs]\n", "    total_length = sum(lengths)\n", "    \n", "    # Calculate N50\n", "    lengths_sorted = sorted(lengths, reverse=True)\n", "    cumulative_length = 0\n", "    n50 = 0\n", "    \n", "    for length in lengths_sorted:\n", "        cumulative_length += length\n", "        if cumulative_length >= total_length / 2:\n", "            n50 = length\n", "            break\n", "    \n", "    metrics = {\n", "        'num_contigs': len(contigs),\n", "        'total_length': total_length,\n", "        'n50': n50,\n", "        'longest_contig': max(lengths),\n", "        'shortest_contig': min(lengths),\n", "        'mean_length': np.mean(lengths),\n", "        'gc_content': np.mean([sum(1 for base in str(contig.seq) if base in 'GC') / len(contig.seq) \n", "                              for contig in contigs])\n", "    }\n", "    \n", "    return metrics\n", "\n", "# Run assembly for all samples\n", "assembly_results = {}\n", "for sample_id in samples.keys():\n", "    print(f\"\\n🧬 Assembling {sample_id}...\")\n", "    \n", "    r1_trimmed = qc_results[sample_id]['r1_trimmed']\n", "    r2_trimmed = qc_results[sample_id]['r2_trimmed']\n", "    \n", "    contigs_file, scaffolds_file = run_spades_assembly(sample_id, r1_trimmed, r2_trimmed, config['output_dir'])\n", "    \n", "    # Analyze assembly quality\n", "    quality_metrics = analyze_assembly_quality(contigs_file)\n", "    \n", "    assembly_results[sample_id] = {\n", "        'contigs_file': contigs_file,\n", "        'scaffolds_file': scaffolds_file,\n", "        'quality_metrics': quality_metrics\n", "    }\n", "    \n", "    print(f\"  Assembly metrics:\")\n", "    for metric, value in quality_metrics.items():\n", "        if isinstance(value, float):\n", "            print(f\"    {metric}: {value:.2f}\")\n", "        else:\n", "            print(f\"    {metric}: {value}\")\n", "\n", "print(\"\\n✅ Genome assembly completed for all samples\")"], "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}