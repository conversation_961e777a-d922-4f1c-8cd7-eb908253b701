{"version": 3, "sources": ["../../../../src/build/webpack/plugins/react-loadable-plugin.ts"], "names": ["ReactLoadablePlugin", "getModuleId", "compilation", "module", "chunkGraph", "getModuleFromDependency", "dep", "moduleGraph", "getModule", "getOriginModuleFromDependency", "getParentModule", "getChunkGroupFromBlock", "block", "getBlockChunkGroup", "buildManifest", "_compiler", "projectSrcDir", "dev", "manifest", "handleBlock", "blocks", "for<PERSON>ach", "chunkGroup", "dependency", "dependencies", "type", "startsWith", "originModule", "originRequest", "resource", "key", "path", "relative", "request", "files", "Set", "file", "add", "chunk", "chunks", "endsWith", "match", "id", "Array", "from", "modules", "Object", "keys", "sort", "reduce", "a", "c", "constructor", "opts", "filename", "pagesOrAppDir", "pagesDir", "appDir", "runtimeAsset", "createAssets", "compiler", "assets", "dirname", "undefined", "sources", "RawSource", "JSON", "stringify", "apply", "hooks", "make", "tap", "processAssets", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;AAmBA,GACA,oFAAoF;AACpF,sEAAsE;;;;;+BA+HzDA;;;eAAAA;;;yBA7HoB;6DAEhB;;;;;;AAEjB,SAASC,YAAYC,WAAgB,EAAEC,MAAW;IAChD,OAAOD,YAAYE,UAAU,CAACH,WAAW,CAACE;AAC5C;AAEA,SAASE,wBACPH,WAAgB,EAChBI,GAAQ;IAER,OAAOJ,YAAYK,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEA,SAASG,8BACPP,WAAgB,EAChBI,GAAQ;IAER,OAAOJ,YAAYK,WAAW,CAACG,eAAe,CAACJ;AACjD;AAEA,SAASK,uBACPT,WAAgB,EAChBU,KAAU;IAEV,OAAOV,YAAYE,UAAU,CAACS,kBAAkB,CAACD;AACnD;AAEA,SAASE,cACPC,SAA2B,EAC3Bb,WAAgC,EAChCc,aAAiC,EACjCC,GAAY;IAEZ,IAAI,CAACD,eAAe;QAClB,OAAO,CAAC;IACV;IACA,IAAIE,WAAsE,CAAC;IAE3E,mBAAmB;IACnB,0CAA0C;IAE1C,yBAAyB;IACzB,yEAAyE;IACzE,yDAAyD;IAEzD,sEAAsE;IACtE,MAAMC,cAAc,CAACP;QACnBA,MAAMQ,MAAM,CAACC,OAAO,CAACF;QACrB,MAAMG,aAAaX,uBAAuBT,aAAaU;QACvD,KAAK,MAAMW,cAAcX,MAAMY,YAAY,CAAE;YAC3C,IAAID,WAAWE,IAAI,CAACC,UAAU,CAAC,aAAa;gBAC1C,4BAA4B;gBAC5B,MAAMvB,SAASE,wBAAwBH,aAAaqB;gBACpD,IAAI,CAACpB,QAAQ;gBAEb,yCAAyC;gBACzC,MAAMwB,eAAelB,8BACnBP,aACAqB;gBAEF,MAAMK,gBAAoCD,gCAAAA,aAAcE,QAAQ;gBAChE,IAAI,CAACD,eAAe;gBAEpB,6DAA6D;gBAC7D,yDAAyD;gBACzD,0DAA0D;gBAC1D,MAAME,MAAM,CAAC,EAAEC,aAAI,CAACC,QAAQ,CAAChB,eAAeY,eAAe,IAAI,EAC7DL,WAAWU,OAAO,CACnB,CAAC;gBAEF,4CAA4C;gBAC5C,MAAMC,QAAQ,IAAIC;gBAElB,IAAIjB,QAAQ,CAACY,IAAI,EAAE;oBACjB,iDAAiD;oBACjD,kDAAkD;oBAClD,gDAAgD;oBAChD,2CAA2C;oBAC3C,6CAA6C;oBAC7C,KAAK,MAAMM,QAAQlB,QAAQ,CAACY,IAAI,CAACI,KAAK,CAAE;wBACtCA,MAAMG,GAAG,CAACD;oBACZ;gBACF;gBAEA,oDAAoD;gBACpD,qDAAqD;gBACrD,6BAA6B;gBAC7B,IAAId,YAAY;oBACd,KAAK,MAAMgB,SAAS,AAAChB,WAClBiB,MAAM,CAAmC;wBAC1CD,MAAMJ,KAAK,CAACb,OAAO,CAAC,CAACe;4BACnB,IACE,AAACA,CAAAA,KAAKI,QAAQ,CAAC,UAAUJ,KAAKI,QAAQ,CAAC,OAAM,KAC7CJ,KAAKK,KAAK,CAAC,4BACX;gCACAP,MAAMG,GAAG,CAACD;4BACZ;wBACF;oBACF;gBACF;gBAEA,qDAAqD;gBACrD,sDAAsD;gBACtD,wDAAwD;gBAExD,uCAAuC;gBACvC,MAAMM,KAAKzB,MAAMa,MAAM7B,YAAYC,aAAaC;gBAChDe,QAAQ,CAACY,IAAI,GAAG;oBAAEY;oBAAIR,OAAOS,MAAMC,IAAI,CAACV;gBAAO;YACjD;QACF;IACF;IACA,KAAK,MAAM/B,UAAUD,YAAY2C,OAAO,CAAE;QACxC1C,OAAOiB,MAAM,CAACC,OAAO,CAACF;IACxB;IAEAD,WAAW4B,OAAOC,IAAI,CAAC7B,UACpB8B,IAAI,EACL,wCAAwC;KACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGjC,QAAQ,CAACiC,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAEhD,OAAOhC;AACT;AAEO,MAAMlB;IAMXoD,YAAYC,IAMX,CAAE;QACD,IAAI,CAACC,QAAQ,GAAGD,KAAKC,QAAQ;QAC7B,IAAI,CAACC,aAAa,GAAGF,KAAKG,QAAQ,IAAIH,KAAKI,MAAM;QACjD,IAAI,CAACC,YAAY,GAAGL,KAAKK,YAAY;QACrC,IAAI,CAACzC,GAAG,GAAGoC,KAAKpC,GAAG;IACrB;IAEA0C,aAAaC,QAAa,EAAE1D,WAAgB,EAAE2D,MAAW,EAAE;QACzD,MAAM7C,gBAAgB,IAAI,CAACuC,aAAa,GACpCxB,aAAI,CAAC+B,OAAO,CAAC,IAAI,CAACP,aAAa,IAC/BQ;QACJ,MAAM7C,WAAWJ,cACf8C,UACA1D,aACAc,eACA,IAAI,CAACC,GAAG;QAGV4C,MAAM,CAAC,IAAI,CAACP,QAAQ,CAAC,GAAG,IAAIU,gBAAO,CAACC,SAAS,CAC3CC,KAAKC,SAAS,CAACjD,UAAU,MAAM;QAEjC,IAAI,IAAI,CAACwC,YAAY,EAAE;YACrBG,MAAM,CAAC,IAAI,CAACH,YAAY,CAAC,GAAG,IAAIM,gBAAO,CAACC,SAAS,CAC/C,CAAC,+BAA+B,EAAEC,KAAKC,SAAS,CAC9CD,KAAKC,SAAS,CAACjD,WACf,CAAC;QAEP;QACA,OAAO2C;IACT;IAEAO,MAAMR,QAA0B,EAAE;QAChCA,SAASS,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,yBAAyB,CAACrE;YAChDA,YAAYmE,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEE,MAAM;gBACNC,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAAChB;gBACC,IAAI,CAACF,YAAY,CAACC,UAAU1D,aAAa2D;YAC3C;QAEJ;IACF;AACF"}