#!/usr/bin/env python3
"""
KAVYA AMR System - Frontend Server
Simple HTTP server to serve the complete frontend interface
"""

import http.server
import socketserver
import os
import mimetypes
from pathlib import Path

PORT = 3001

class FrontendHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler for serving frontend files"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(Path(__file__).parent), **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_frontend()
        else:
            super().do_GET()
    
    def serve_frontend(self):
        """Serve the main frontend interface"""
        try:
            frontend_path = Path(__file__).parent / "frontend_complete.html"
            with open(frontend_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Error serving frontend: {e}")
    
    def end_headers(self):
        """Add CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def main():
    """Start the frontend server"""
    print("🎨 KAVYA AMR System - Frontend Server")
    print("=" * 50)
    print(f"🚀 Starting frontend server on port {PORT}...")
    print(f"📍 Frontend will be available at: http://localhost:{PORT}")
    print(f"🔗 Backend API: http://localhost:8001")
    print("=" * 50)
    print("Press Ctrl+C to stop the server")
    print()
    
    try:
        with socketserver.TCPServer(("", PORT), FrontendHandler) as httpd:
            print(f"✅ Frontend server running on http://localhost:{PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Frontend server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == "__main__":
    main()
