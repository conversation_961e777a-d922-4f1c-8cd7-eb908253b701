"""
KAVYA AMR System - Nanobot Physics Simulation Engine
Advanced physics-based simulation for nanobot navigation and drug delivery
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import logging
from scipy.integrate import odeint
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

logger = logging.getLogger(__name__)


@dataclass
class NanobotState:
    """State representation for a single nanobot"""
    position: np.ndarray  # 3D position [x, y, z]
    velocity: np.ndarray  # 3D velocity [vx, vy, vz]
    orientation: np.ndarray  # Quaternion [w, x, y, z]
    payload_mass: float  # Current payload mass
    energy_level: float  # Battery/energy level (0-1)
    active: bool = True
    target_reached: bool = False
    
    def __post_init__(self):
        """Ensure arrays are numpy arrays"""
        self.position = np.array(self.position, dtype=np.float64)
        self.velocity = np.array(self.velocity, dtype=np.float64)
        self.orientation = np.array(self.orientation, dtype=np.float64)


@dataclass
class Environment:
    """Biological environment representation"""
    dimensions: Tuple[float, float, float]  # Environment size [x, y, z] in micrometers
    viscosity: float = 0.001  # Pa·s (blood viscosity)
    temperature: float = 310.15  # K (body temperature)
    ph: float = 7.4  # Blood pH
    flow_field: Optional[np.ndarray] = None  # 3D flow field
    obstacles: List[Dict] = field(default_factory=list)  # Tissue structures, cells
    chemical_gradients: Dict[str, np.ndarray] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize flow field if not provided"""
        if self.flow_field is None:
            # Simple laminar flow in blood vessel
            grid_size = 50
            x = np.linspace(0, self.dimensions[0], grid_size)
            y = np.linspace(0, self.dimensions[1], grid_size)
            z = np.linspace(0, self.dimensions[2], grid_size)
            
            # Parabolic flow profile (Poiseuille flow)
            self.flow_field = np.zeros((grid_size, grid_size, grid_size, 3))
            for i in range(grid_size):
                for j in range(grid_size):
                    r_squared = ((y[j] - self.dimensions[1]/2)**2 + 
                               (z[i] - self.dimensions[2]/2)**2)
                    max_r_squared = (min(self.dimensions[1], self.dimensions[2])/2)**2
                    if r_squared <= max_r_squared:
                        # Parabolic velocity profile
                        velocity_magnitude = 0.1 * (1 - r_squared / max_r_squared)  # m/s
                        self.flow_field[i, j, :, 0] = velocity_magnitude  # Flow in x direction


@dataclass
class NanobotProperties:
    """Physical and chemical properties of nanobots"""
    radius: float = 50e-9  # meters (50 nm)
    mass: float = 1e-18  # kg
    max_payload: float = 1e-19  # kg
    max_velocity: float = 1e-4  # m/s
    energy_capacity: float = 1e-15  # J
    surface_charge: float = -10e-3  # C/m²
    magnetic_moment: float = 1e-21  # A·m²
    drug_release_rate: float = 1e-21  # kg/s
    biocompatibility_score: float = 0.95
    targeting_specificity: float = 0.9


class PhysicsEngine:
    """
    Core physics engine for nanobot simulation
    Handles Brownian motion, fluid dynamics, electromagnetic forces
    """
    
    def __init__(self, 
                 environment: Environment,
                 dt: float = 1e-6,  # 1 microsecond timestep
                 random_seed: int = 42):
        self.environment = environment
        self.dt = dt
        self.time = 0.0
        np.random.seed(random_seed)
        
        # Physical constants
        self.k_b = 1.380649e-23  # Boltzmann constant
        self.eta = environment.viscosity  # Dynamic viscosity
        
    def update_nanobot(self, nanobot: NanobotState, properties: NanobotProperties, 
                      target_position: np.ndarray, external_fields: Dict = None) -> NanobotState:
        """
        Update nanobot state for one timestep
        
        Args:
            nanobot: Current nanobot state
            properties: Nanobot physical properties
            target_position: Target coordinates
            external_fields: External electromagnetic fields
            
        Returns:
            Updated nanobot state
        """
        if not nanobot.active:
            return nanobot
        
        # Calculate forces
        forces = self._calculate_forces(nanobot, properties, target_position, external_fields)
        
        # Update velocity (Newton's second law)
        acceleration = forces / properties.mass
        new_velocity = nanobot.velocity + acceleration * self.dt
        
        # Apply velocity limits
        speed = np.linalg.norm(new_velocity)
        if speed > properties.max_velocity:
            new_velocity = new_velocity * properties.max_velocity / speed
        
        # Update position (Euler integration)
        new_position = nanobot.position + new_velocity * self.dt
        
        # Boundary conditions
        new_position = self._apply_boundary_conditions(new_position)
        
        # Update energy consumption
        energy_consumed = self._calculate_energy_consumption(nanobot, properties, forces)
        new_energy = max(0.0, nanobot.energy_level - energy_consumed)
        
        # Check if target reached
        distance_to_target = np.linalg.norm(new_position - target_position)
        target_reached = distance_to_target < properties.radius * 2
        
        # Update payload (drug release)
        new_payload = nanobot.payload_mass
        if target_reached and nanobot.payload_mass > 0:
            release_amount = min(properties.drug_release_rate * self.dt, nanobot.payload_mass)
            new_payload -= release_amount
        
        # Create updated state
        updated_state = NanobotState(
            position=new_position,
            velocity=new_velocity,
            orientation=nanobot.orientation,  # Simplified - not updating orientation
            payload_mass=new_payload,
            energy_level=new_energy,
            active=new_energy > 0.01,  # Deactivate if energy too low
            target_reached=target_reached
        )
        
        return updated_state
    
    def _calculate_forces(self, nanobot: NanobotState, properties: NanobotProperties,
                         target_position: np.ndarray, external_fields: Dict = None) -> np.ndarray:
        """Calculate all forces acting on the nanobot"""
        total_force = np.zeros(3)
        
        # 1. Brownian motion (random thermal force)
        brownian_force = self._brownian_force(properties)
        total_force += brownian_force
        
        # 2. Drag force (Stokes drag)
        drag_force = self._drag_force(nanobot, properties)
        total_force += drag_force
        
        # 3. Flow field force
        flow_force = self._flow_field_force(nanobot, properties)
        total_force += flow_force
        
        # 4. Electromagnetic guidance force
        if external_fields:
            em_force = self._electromagnetic_force(nanobot, properties, external_fields)
            total_force += em_force
        
        # 5. Chemical gradient force (chemotaxis)
        gradient_force = self._chemical_gradient_force(nanobot, target_position, properties)
        total_force += gradient_force
        
        # 6. Obstacle avoidance force
        obstacle_force = self._obstacle_avoidance_force(nanobot, properties)
        total_force += obstacle_force
        
        return total_force
    
    def _brownian_force(self, properties: NanobotProperties) -> np.ndarray:
        """Calculate random Brownian motion force"""
        # Einstein relation: D = k_B * T / (6 * π * η * r)
        diffusion_coefficient = (self.k_b * self.environment.temperature / 
                               (6 * np.pi * self.eta * properties.radius))
        
        # Random force with proper scaling
        force_magnitude = np.sqrt(2 * self.k_b * self.environment.temperature * 
                                self.eta * 6 * np.pi * properties.radius / self.dt)
        
        random_direction = np.random.randn(3)
        random_direction /= np.linalg.norm(random_direction)
        
        return force_magnitude * random_direction
    
    def _drag_force(self, nanobot: NanobotState, properties: NanobotProperties) -> np.ndarray:
        """Calculate Stokes drag force"""
        # Stokes drag: F = -6πηrv
        drag_coefficient = 6 * np.pi * self.eta * properties.radius
        return -drag_coefficient * nanobot.velocity
    
    def _flow_field_force(self, nanobot: NanobotState, properties: NanobotProperties) -> np.ndarray:
        """Calculate force from fluid flow field"""
        if self.environment.flow_field is None:
            return np.zeros(3)
        
        # Interpolate flow velocity at nanobot position
        flow_velocity = self._interpolate_flow_velocity(nanobot.position)
        
        # Relative velocity between nanobot and fluid
        relative_velocity = flow_velocity - nanobot.velocity
        
        # Drag force in flowing fluid
        drag_coefficient = 6 * np.pi * self.eta * properties.radius
        return drag_coefficient * relative_velocity
    
    def _electromagnetic_force(self, nanobot: NanobotState, properties: NanobotProperties,
                             external_fields: Dict) -> np.ndarray:
        """Calculate electromagnetic guidance force"""
        force = np.zeros(3)
        
        # Magnetic force: F = ∇(μ·B)
        if 'magnetic_field' in external_fields and 'magnetic_gradient' in external_fields:
            magnetic_force = (properties.magnetic_moment * 
                            external_fields['magnetic_gradient'])
            force += magnetic_force
        
        # Electric force: F = qE
        if 'electric_field' in external_fields:
            # Surface charge on nanobot
            total_charge = properties.surface_charge * 4 * np.pi * properties.radius**2
            electric_force = total_charge * external_fields['electric_field']
            force += electric_force
        
        return force
    
    def _chemical_gradient_force(self, nanobot: NanobotState, target_position: np.ndarray,
                               properties: NanobotProperties) -> np.ndarray:
        """Calculate chemotactic force toward target"""
        # Simplified chemotaxis model
        direction_to_target = target_position - nanobot.position
        distance_to_target = np.linalg.norm(direction_to_target)
        
        if distance_to_target < 1e-9:  # Very close to target
            return np.zeros(3)
        
        # Normalize direction
        direction_to_target /= distance_to_target
        
        # Force magnitude decreases with distance (inverse square law)
        force_magnitude = properties.targeting_specificity * 1e-12 / (distance_to_target**2 + 1e-12)
        
        return force_magnitude * direction_to_target
    
    def _obstacle_avoidance_force(self, nanobot: NanobotState, properties: NanobotProperties) -> np.ndarray:
        """Calculate repulsive force from obstacles (cells, tissues)"""
        total_force = np.zeros(3)
        
        for obstacle in self.environment.obstacles:
            obstacle_pos = np.array(obstacle['position'])
            obstacle_radius = obstacle['radius']
            
            # Vector from obstacle to nanobot
            separation = nanobot.position - obstacle_pos
            distance = np.linalg.norm(separation)
            
            # Minimum separation distance
            min_distance = properties.radius + obstacle_radius
            
            if distance < min_distance * 2:  # Within interaction range
                if distance > 0:
                    # Repulsive force (inverse square)
                    force_magnitude = 1e-12 / (distance**2 + 1e-15)
                    force_direction = separation / distance
                    total_force += force_magnitude * force_direction
        
        return total_force
    
    def _interpolate_flow_velocity(self, position: np.ndarray) -> np.ndarray:
        """Interpolate flow velocity at given position"""
        if self.environment.flow_field is None:
            return np.zeros(3)
        
        # Simple nearest neighbor interpolation
        # In practice, would use trilinear interpolation
        grid_shape = self.environment.flow_field.shape[:3]
        
        # Convert position to grid indices
        x_idx = int(position[0] / self.environment.dimensions[0] * (grid_shape[2] - 1))
        y_idx = int(position[1] / self.environment.dimensions[1] * (grid_shape[1] - 1))
        z_idx = int(position[2] / self.environment.dimensions[2] * (grid_shape[0] - 1))
        
        # Clamp indices
        x_idx = np.clip(x_idx, 0, grid_shape[2] - 1)
        y_idx = np.clip(y_idx, 0, grid_shape[1] - 1)
        z_idx = np.clip(z_idx, 0, grid_shape[0] - 1)
        
        return self.environment.flow_field[z_idx, y_idx, x_idx]
    
    def _apply_boundary_conditions(self, position: np.ndarray) -> np.ndarray:
        """Apply boundary conditions to keep nanobot in environment"""
        # Reflective boundaries
        new_position = position.copy()
        
        for i in range(3):
            if new_position[i] < 0:
                new_position[i] = -new_position[i]
            elif new_position[i] > self.environment.dimensions[i]:
                new_position[i] = 2 * self.environment.dimensions[i] - new_position[i]
        
        return new_position
    
    def _calculate_energy_consumption(self, nanobot: NanobotState, properties: NanobotProperties,
                                    forces: np.ndarray) -> float:
        """Calculate energy consumed in this timestep"""
        # Energy for movement (work done against drag)
        movement_energy = np.dot(forces, nanobot.velocity) * self.dt
        
        # Base metabolic energy
        base_energy = 1e-18 * self.dt  # J/s
        
        # Total energy consumed (normalized by capacity)
        total_energy = (abs(movement_energy) + base_energy) / properties.energy_capacity
        
        return total_energy


class NanobotSwarm:
    """
    Manages multiple nanobots in a coordinated swarm
    """
    
    def __init__(self, 
                 num_nanobots: int,
                 environment: Environment,
                 properties: NanobotProperties,
                 initial_positions: Optional[np.ndarray] = None):
        self.environment = environment
        self.properties = properties
        self.physics_engine = PhysicsEngine(environment)
        
        # Initialize nanobots
        self.nanobots = []
        if initial_positions is None:
            # Random initial positions
            for i in range(num_nanobots):
                position = np.random.rand(3) * np.array(environment.dimensions) * 0.1
                velocity = np.zeros(3)
                orientation = np.array([1, 0, 0, 0])  # Identity quaternion
                
                nanobot = NanobotState(
                    position=position,
                    velocity=velocity,
                    orientation=orientation,
                    payload_mass=properties.max_payload,
                    energy_level=1.0
                )
                self.nanobots.append(nanobot)
        else:
            for i in range(num_nanobots):
                nanobot = NanobotState(
                    position=initial_positions[i],
                    velocity=np.zeros(3),
                    orientation=np.array([1, 0, 0, 0]),
                    payload_mass=properties.max_payload,
                    energy_level=1.0
                )
                self.nanobots.append(nanobot)
        
        # Simulation state
        self.time = 0.0
        self.trajectory_history = []
        
    def simulate_step(self, target_positions: np.ndarray, external_fields: Dict = None) -> Dict:
        """
        Simulate one timestep for all nanobots
        
        Args:
            target_positions: Target positions for each nanobot
            external_fields: External electromagnetic fields
            
        Returns:
            Simulation state information
        """
        # Update each nanobot
        for i, nanobot in enumerate(self.nanobots):
            if i < len(target_positions):
                target = target_positions[i]
            else:
                target = target_positions[0]  # Default to first target
            
            self.nanobots[i] = self.physics_engine.update_nanobot(
                nanobot, self.properties, target, external_fields
            )
        
        # Update time
        self.time += self.physics_engine.dt
        
        # Record trajectory
        positions = np.array([nb.position for nb in self.nanobots])
        self.trajectory_history.append(positions.copy())
        
        # Calculate swarm statistics
        stats = self._calculate_swarm_statistics(target_positions)
        
        return stats
    
    def _calculate_swarm_statistics(self, target_positions: np.ndarray) -> Dict:
        """Calculate statistics for the nanobot swarm"""
        active_nanobots = [nb for nb in self.nanobots if nb.active]
        
        if not active_nanobots:
            return {
                'active_count': 0,
                'average_distance_to_target': float('inf'),
                'average_energy': 0.0,
                'average_payload': 0.0,
                'targets_reached': 0,
                'swarm_cohesion': 0.0
            }
        
        # Calculate distances to targets
        distances = []
        for i, nanobot in enumerate(active_nanobots):
            if i < len(target_positions):
                target = target_positions[i]
            else:
                target = target_positions[0]
            distance = np.linalg.norm(nanobot.position - target)
            distances.append(distance)
        
        # Calculate swarm cohesion (average inter-nanobot distance)
        positions = np.array([nb.position for nb in active_nanobots])
        if len(positions) > 1:
            pairwise_distances = cdist(positions, positions)
            swarm_cohesion = np.mean(pairwise_distances[np.triu_indices_from(pairwise_distances, k=1)])
        else:
            swarm_cohesion = 0.0
        
        return {
            'active_count': len(active_nanobots),
            'average_distance_to_target': np.mean(distances),
            'average_energy': np.mean([nb.energy_level for nb in active_nanobots]),
            'average_payload': np.mean([nb.payload_mass for nb in active_nanobots]),
            'targets_reached': sum(1 for nb in self.nanobots if nb.target_reached),
            'swarm_cohesion': swarm_cohesion,
            'simulation_time': self.time
        }
    
    def get_positions(self) -> np.ndarray:
        """Get current positions of all nanobots"""
        return np.array([nb.position for nb in self.nanobots])
    
    def get_active_nanobots(self) -> List[NanobotState]:
        """Get list of active nanobots"""
        return [nb for nb in self.nanobots if nb.active]
    
    def visualize_swarm(self, target_positions: np.ndarray = None, save_path: str = None):
        """Visualize current swarm state"""
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # Plot nanobots
        positions = self.get_positions()
        active_mask = [nb.active for nb in self.nanobots]
        
        # Active nanobots in blue
        if np.any(active_mask):
            ax.scatter(positions[active_mask, 0], positions[active_mask, 1], 
                      positions[active_mask, 2], c='blue', s=50, label='Active Nanobots')
        
        # Inactive nanobots in red
        if not np.all(active_mask):
            ax.scatter(positions[~np.array(active_mask), 0], positions[~np.array(active_mask), 1], 
                      positions[~np.array(active_mask), 2], c='red', s=50, label='Inactive Nanobots')
        
        # Plot targets
        if target_positions is not None:
            ax.scatter(target_positions[:, 0], target_positions[:, 1], 
                      target_positions[:, 2], c='green', s=100, marker='*', label='Targets')
        
        # Plot obstacles
        for obstacle in self.environment.obstacles:
            pos = obstacle['position']
            radius = obstacle['radius']
            # Simple sphere representation
            u = np.linspace(0, 2 * np.pi, 20)
            v = np.linspace(0, np.pi, 20)
            x = radius * np.outer(np.cos(u), np.sin(v)) + pos[0]
            y = radius * np.outer(np.sin(u), np.sin(v)) + pos[1]
            z = radius * np.outer(np.ones(np.size(u)), np.cos(v)) + pos[2]
            ax.plot_surface(x, y, z, alpha=0.3, color='gray')
        
        ax.set_xlabel('X (μm)')
        ax.set_ylabel('Y (μm)')
        ax.set_zlabel('Z (μm)')
        ax.set_title(f'Nanobot Swarm at t={self.time:.2e}s')
        ax.legend()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        else:
            plt.show()
        
        plt.close()
