'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BeakerIcon, 
  CpuChipIcon, 
  GlobeAltIcon, 
  ShieldCheckIcon,
  ChartBarIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline'
import { Hero } from '@/components/Hero'
import { FeatureCard } from '@/components/FeatureCard'
import { StatsSection } from '@/components/StatsSection'
import { CTASection } from '@/components/CTASection'

const features = [
  {
    icon: BeakerIcon,
    title: 'AI-Powered Diagnostics',
    description: 'Advanced computer vision and genomic analysis for rapid pathogen identification and resistance prediction.',
    color: 'blue'
  },
  {
    icon: CpuChipIcon,
    title: 'Quantum-Enhanced Analysis',
    description: 'Quantum computing algorithms for protein folding simulation and novel resistance mechanism discovery.',
    color: 'purple'
  },
  {
    icon: ShieldCheckIcon,
    title: 'Nanobot Therapy',
    description: 'Precision-guided nanobots for targeted antimicrobial delivery with real-time monitoring.',
    color: 'green'
  },
  {
    icon: GlobeAltIcon,
    title: 'Global Surveillance',
    description: 'Federated learning network for worldwide AMR monitoring and outbreak prediction.',
    color: 'red'
  },
  {
    icon: ChartBarIcon,
    title: 'Real-Time Analytics',
    description: 'Comprehensive dashboards with predictive analytics and treatment optimization.',
    color: 'yellow'
  },
  {
    icon: LightBulbIcon,
    title: 'Ethical AI',
    description: 'Responsible AI with bias detection, explainability, and privacy-preserving technologies.',
    color: 'indigo'
  }
]

const stats = [
  { label: 'Diagnostic Accuracy', value: '95%+', description: 'Pathogen identification accuracy' },
  { label: 'Analysis Speed', value: '<30s', description: 'Complete diagnostic analysis' },
  { label: 'Global Reach', value: '50+', description: 'Countries in surveillance network' },
  { label: 'Lives Saved', value: '10K+', description: 'Through early AMR detection' }
]

export default function HomePage() {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Hero />

      {/* Features Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Revolutionary AMR Management Platform
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Combining artificial intelligence, quantum computing, and nanotechnology 
              to combat antimicrobial resistance and save lives worldwide.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
                transition={{ duration: 0.8, delay: 0.3 + index * 0.1 }}
              >
                <FeatureCard {...feature} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <StatsSection stats={stats} />

      {/* Technology Stack Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Cutting-Edge Technology Stack
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Built with the latest advances in AI, quantum computing, and biotechnology
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { name: 'PyTorch', logo: '🔥', description: 'Deep Learning' },
              { name: 'Qiskit', logo: '⚛️', description: 'Quantum Computing' },
              { name: 'Next.js', logo: '▲', description: 'Frontend Framework' },
              { name: 'FastAPI', logo: '⚡', description: 'Backend API' },
              { name: 'Kubernetes', logo: '☸️', description: 'Orchestration' },
              { name: 'PostgreSQL', logo: '🐘', description: 'Database' },
              { name: 'Docker', logo: '🐳', description: 'Containerization' },
              { name: 'Prometheus', logo: '📊', description: 'Monitoring' }
            ].map((tech, index) => (
              <motion.div
                key={tech.name}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: isLoaded ? 1 : 0, scale: isLoaded ? 1 : 0.8 }}
                transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="text-4xl mb-2">{tech.logo}</div>
                <h3 className="font-semibold text-gray-900">{tech.name}</h3>
                <p className="text-sm text-gray-600">{tech.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Clinical Applications
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Transforming healthcare across multiple clinical scenarios
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: 'Emergency Department',
                description: 'Rapid sepsis diagnosis and treatment optimization in critical care settings.',
                image: '🏥',
                benefits: ['<30 min diagnosis', 'Reduced mortality', 'Optimized antibiotics']
              },
              {
                title: 'Intensive Care Unit',
                description: 'Real-time monitoring of critically ill patients with AMR infections.',
                image: '🏥',
                benefits: ['Continuous monitoring', 'Personalized therapy', 'Outcome prediction']
              },
              {
                title: 'Outpatient Clinics',
                description: 'Point-of-care testing for routine infections and resistance screening.',
                image: '🏥',
                benefits: ['Immediate results', 'Reduced visits', 'Better outcomes']
              }
            ].map((useCase, index) => (
              <motion.div
                key={useCase.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
                transition={{ duration: 0.8, delay: 0.9 + index * 0.1 }}
                className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-8 hover:shadow-lg transition-shadow"
              >
                <div className="text-6xl mb-4 text-center">{useCase.image}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{useCase.title}</h3>
                <p className="text-gray-600 mb-4">{useCase.description}</p>
                <ul className="space-y-2">
                  {useCase.benefits.map((benefit, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-700">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <CTASection />
    </div>
  )
}
