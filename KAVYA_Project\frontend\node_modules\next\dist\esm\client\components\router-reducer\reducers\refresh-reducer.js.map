{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "names": ["fetchServerResponse", "createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "handleMutable", "fillLazyItemsTillLeafWithHead", "createEmptyCacheNode", "handleSegmentMismatch", "hasInterceptionRouteInCurrentTree", "refreshInactiveParallelSegments", "refreshReducer", "state", "action", "origin", "mutable", "href", "canonicalUrl", "currentTree", "tree", "preserveCustomHistoryState", "cache", "includeNextUrl", "lazyData", "URL", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "canonicalUrlOverrideHref", "undefined", "cacheNodeSeedData", "head", "slice", "rsc", "prefetchRsc", "prefetchCache", "Map", "updatedTree", "updatedCache", "patchedTree"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,SAASC,6BAA6B,QAAQ,yCAAwC;AACtF,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,qBAAqB,QAAQ,6BAA4B;AAClE,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,+BAA+B,QAAQ,wCAAuC;AAEvF,OAAO,SAASC,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/B,IAAIC,cAAcN,MAAMO,IAAI;IAE5BJ,QAAQK,0BAA0B,GAAG;IAErC,MAAMC,QAAmBd;IAEzB,sFAAsF;IACtF,sHAAsH;IACtH,MAAMe,iBAAiBb,kCAAkCG,MAAMO,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCE,MAAME,QAAQ,GAAGvB,oBACf,IAAIwB,IAAIR,MAAMF,SACd;QAACI,WAAW,CAAC,EAAE;QAAEA,WAAW,CAAC,EAAE;QAAEA,WAAW,CAAC,EAAE;QAAE;KAAU,EAC3DI,iBAAiBV,MAAMa,OAAO,GAAG,MACjCb,MAAMc,OAAO;IAGf,OAAOL,MAAME,QAAQ,CAACI,IAAI,CACxB;YAAO,CAACC,YAAYC,qBAAqB;QACvC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOxB,kBACLQ,OACAG,SACAa,YACAhB,MAAMkB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/DV,MAAME,QAAQ,GAAG;QAEjB,KAAK,MAAMS,kBAAkBJ,WAAY;YACvC,oFAAoF;YACpF,IAAII,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOvB;YACT;YAEA,mGAAmG;YACnG,MAAM,CAACwB,UAAU,GAAGJ;YACpB,MAAMK,UAAUnC,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJgB,aACAkB,WACAxB,MAAMK,YAAY;YAGpB,IAAIoB,YAAY,MAAM;gBACpB,OAAO7B,sBAAsBI,OAAOC,QAAQuB;YAC9C;YAEA,IAAIjC,4BAA4Be,aAAamB,UAAU;gBACrD,OAAOjC,kBACLQ,OACAG,SACAC,MACAJ,MAAMkB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMO,2BAA2BT,uBAC7B5B,kBAAkB4B,wBAClBU;YAEJ,IAAIV,sBAAsB;gBACxBd,QAAQE,YAAY,GAAGqB;YACzB;YAEA,0DAA0D;YAC1D,MAAM,CAACE,mBAAmBC,KAAK,GAAGT,eAAeU,KAAK,CAAC,CAAC;YAExD,8FAA8F;YAC9F,IAAIF,sBAAsB,MAAM;gBAC9B,MAAMG,MAAMH,iBAAiB,CAAC,EAAE;gBAChCnB,MAAMsB,GAAG,GAAGA;gBACZtB,MAAMuB,WAAW,GAAG;gBACpBtC,8BACEe,OACA,4FAA4F;gBAC5FkB,WACAH,WACAI,mBACAC;gBAEF1B,QAAQ8B,aAAa,GAAG,IAAIC;YAC9B;YAEA,MAAMpC,gCAAgC;gBACpCE;gBACAmC,aAAaV;gBACbW,cAAc3B;gBACdC;gBACAL,cAAcF,QAAQE,YAAY,IAAIL,MAAMK,YAAY;YAC1D;YAEAF,QAAQM,KAAK,GAAGA;YAChBN,QAAQkC,WAAW,GAAGZ;YACtBtB,QAAQE,YAAY,GAAGD;YAEvBE,cAAcmB;QAChB;QAEA,OAAOhC,cAAcO,OAAOG;IAC9B,GACA,IAAMH;AAEV"}