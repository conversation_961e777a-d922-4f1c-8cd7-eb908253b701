#!/usr/bin/env python3
"""
KAVYA System - Final Comprehensive Test
Tests all real AI models and system components
"""

import requests
import json
import time
import sys
from datetime import datetime

def test_endpoint(url, method='GET', data=None, timeout=30):
    """Test an API endpoint"""
    try:
        start_time = time.time()
        
        if method == 'GET':
            response = requests.get(url, timeout=timeout)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=timeout)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        return {
            'success': True,
            'status_code': response.status_code,
            'response_time': response_time,
            'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'response_time': None
        }

def main():
    """Run comprehensive system tests"""
    print("🧬 KAVYA System - Final Comprehensive Test")
    print("=" * 60)
    print(f"🕒 Test started at: {datetime.now()}")
    print()
    
    base_url = "http://localhost:8001"
    
    tests = [
        {
            'name': 'System Health Check',
            'url': f'{base_url}/health',
            'method': 'GET'
        },
        {
            'name': 'System Status',
            'url': f'{base_url}/api/v1/system/status',
            'method': 'GET'
        },
        {
            'name': 'Real AI Diagnostic Analysis',
            'url': f'{base_url}/api/v1/diagnostic/analyze',
            'method': 'POST',
            'data': {
                "patient_id": "final_test_patient",
                "sample_type": "blood",
                "genomic_data": "ATGAAAAAAGCAATACTTAGATTTCAAGCTATACCAAGCATTATTGAAGCAGGCATCGCCATGGGTCACGACCTCGAAACCAACGATCAAATCGCCAAGCTCAAGAACGCAATCGAAGAA",
                "clinical_data": {
                    "fever": True,
                    "wbc_count": 22000,
                    "crp": 75.8,
                    "temperature": 39.2
                },
                "priority": "urgent"
            }
        },
        {
            'name': 'Real Quantum Protein Folding',
            'url': f'{base_url}/api/v1/quantum/simulate?protein_sequence=MKLLNVINFVFLMFVSSSKILGYGQFTEST&mutation_sites=2,6,11',
            'method': 'GET'
        },
        {
            'name': 'Real Nanobot Physics Deployment',
            'url': f'{base_url}/api/v1/nanobot/deploy',
            'method': 'POST',
            'data': {
                "patient_id": "nanobot_test_patient",
                "target_pathogen": "Staphylococcus aureus",
                "target_site": {"x": 20.5, "y": 15.2, "z": 8.7},
                "nanobot_count": 750000,
                "therapy_type": "antimicrobial"
            }
        },
        {
            'name': 'Analytics Dashboard',
            'url': f'{base_url}/api/v1/analytics/dashboard',
            'method': 'GET'
        }
    ]
    
    results = []
    
    for test in tests:
        print(f"🧪 Testing: {test['name']}")
        print(f"   URL: {test['url']}")
        
        result = test_endpoint(
            test['url'], 
            test['method'], 
            test.get('data'),
            timeout=60  # Longer timeout for AI models
        )
        
        if result['success']:
            print(f"   ✅ SUCCESS - {result['status_code']} ({result['response_time']:.3f}s)")
            
            # Show key results for AI tests
            if 'diagnostic' in test['name'].lower():
                data = result['data']
                if 'pathogen_identification' in data:
                    pathogen = data['pathogen_identification'].get('primary_pathogen', 'Unknown')
                    confidence = data['pathogen_identification'].get('confidence', 0)
                    print(f"      🦠 Pathogen: {pathogen} (confidence: {confidence:.2f})")
                
            elif 'quantum' in test['name'].lower():
                data = result['data']
                if 'folding_energy' in data:
                    energy = data.get('folding_energy', 0)
                    stability = data.get('stability_score', 0)
                    print(f"      ⚛️ Folding Energy: {energy}, Stability: {stability:.2f}")
                    
            elif 'nanobot' in test['name'].lower():
                data = result['data']
                if 'deployment_id' in data:
                    deployment_id = data.get('deployment_id', 'Unknown')
                    safety = data.get('safety_score', 0)
                    print(f"      🤖 Deployment ID: {deployment_id[:8]}..., Safety: {safety:.2f}")
        else:
            print(f"   ❌ FAILED - {result['error']}")
        
        results.append({
            'test': test['name'],
            'result': result
        })
        print()
    
    # Summary
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    successful_tests = sum(1 for r in results if r['result']['success'])
    total_tests = len(results)
    
    print(f"✅ Successful Tests: {successful_tests}/{total_tests}")
    print(f"❌ Failed Tests: {total_tests - successful_tests}/{total_tests}")
    
    if successful_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! KAVYA SYSTEM IS FULLY OPERATIONAL!")
        print("🚀 Real AI models are working perfectly!")
    else:
        print(f"\n⚠️ {total_tests - successful_tests} tests failed. Check the logs above.")
    
    print(f"\n🕒 Test completed at: {datetime.now()}")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS")
    print("=" * 60)
    for result in results:
        test_name = result['test']
        test_result = result['result']
        
        status = "✅ PASS" if test_result['success'] else "❌ FAIL"
        response_time = f"{test_result['response_time']:.3f}s" if test_result['response_time'] else "N/A"
        
        print(f"{status} | {test_name:<30} | {response_time}")
    
    return successful_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
