#!/usr/bin/env python3
"""
KAVYA AMR System - Development Startup Script
Simple script to start the system in development mode without Dock<PERSON>
"""

import os
import sys
import subprocess
import time
import threading
import signal
from pathlib import Path

def print_banner():
    """Print KAVYA banner"""
    banner = """
 ██╗  ██╗ █████╗ ██╗   ██╗██╗   ██╗ █████╗ 
 ██║ ██╔╝██╔══██╗██║   ██║╚██╗ ██╔╝██╔══██╗
 █████╔╝ ███████║██║   ██║ ╚████╔╝ ███████║
 ██╔═██╗ ██╔══██║╚██╗ ██╔╝  ╚██╔╝  ██╔══██║
 ██║  ██╗██║  ██║ ╚████╔╝    ██║   ██║  ██║
 ╚═╝  ╚═╝╚═╝  ╚═╝  ╚═══╝     ╚═╝   ╚═╝  ╚═╝
                                            
 🧬 AI-Powered Antimicrobial Resistance System
 🚀 Development Mode - Local Startup
"""
    print(banner)

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    # Check Python
    try:
        import fastapi
        import uvicorn
        print("✅ Python backend dependencies available")
    except ImportError as e:
        print(f"❌ Missing Python dependencies: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    
    # Check Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js available: {result.stdout.strip()}")
        else:
            print("❌ Node.js not available")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found")
        return False
    
    return True

def start_backend():
    """Start the FastAPI backend server"""
    print("🔧 Starting backend server...")
    
    # Change to backend directory
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    
    # Start uvicorn server
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload",
            "--log-level", "info"
        ])
    except KeyboardInterrupt:
        print("🛑 Backend server stopped")

def start_ai_models():
    """Start the AI models server"""
    print("🧠 Starting AI models server...")
    
    # Change to ai_models directory
    ai_models_dir = Path(__file__).parent / "ai_models"
    os.chdir(ai_models_dir)
    
    # Start AI models server
    try:
        subprocess.run([
            sys.executable, "server.py"
        ])
    except KeyboardInterrupt:
        print("🛑 AI models server stopped")

def start_frontend():
    """Start the Next.js frontend"""
    print("🎨 Starting frontend server...")
    
    # Change to frontend directory
    frontend_dir = Path(__file__).parent / "frontend"
    os.chdir(frontend_dir)
    
    # Start Next.js development server
    try:
        subprocess.run(["npm", "run", "dev"])
    except KeyboardInterrupt:
        print("🛑 Frontend server stopped")

def create_mock_data():
    """Create mock data directories and files"""
    print("📁 Creating mock data structure...")
    
    # Create directories
    directories = [
        "data/raw",
        "data/processed", 
        "data/genomics",
        "data/images",
        "data/clinical",
        "models",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # Create mock model files
    model_files = [
        "models/pathogen_classifier.pth",
        "models/genomic_transformer.pth", 
        "models/multimodal_fusion.pth",
        "models/quantum_predictor.pkl"
    ]
    
    for model_file in model_files:
        model_path = Path(model_file)
        if not model_path.exists():
            model_path.touch()
            print(f"📄 Created mock model: {model_file}")

def start_simple_mode():
    """Start in simple mode - just backend API"""
    print("🚀 Starting KAVYA in Simple Mode (Backend Only)")
    print("📍 Backend API will be available at: http://localhost:8000")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("❤️  Health Check: http://localhost:8000/health")
    print("\n🔄 Starting backend server...")
    
    # Create mock data
    create_mock_data()
    
    # Start backend
    start_backend()

def start_full_mode():
    """Start in full mode - all services"""
    print("🚀 Starting KAVYA in Full Mode")
    print("📍 Frontend: http://localhost:3000")
    print("📍 Backend API: http://localhost:8000") 
    print("📍 AI Models: http://localhost:8001")
    print("📖 API Documentation: http://localhost:8000/docs")
    
    # Create mock data
    create_mock_data()
    
    # Start services in separate threads
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    ai_models_thread = threading.Thread(target=start_ai_models, daemon=True)
    
    backend_thread.start()
    time.sleep(3)  # Give backend time to start
    
    ai_models_thread.start()
    time.sleep(3)  # Give AI models time to start
    
    # Start frontend in main thread (so we can catch Ctrl+C)
    try:
        start_frontend()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down all services...")

def main():
    """Main function"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed. Please install missing dependencies.")
        sys.exit(1)
    
    # Ask user for mode
    print("\n🎯 Choose startup mode:")
    print("1. Simple Mode (Backend API only)")
    print("2. Full Mode (All services)")
    print("3. Exit")
    
    while True:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            start_simple_mode()
            break
        elif choice == "2":
            start_full_mode()
            break
        elif choice == "3":
            print("👋 Goodbye!")
            sys.exit(0)
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 KAVYA system stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error starting KAVYA: {e}")
        sys.exit(1)
