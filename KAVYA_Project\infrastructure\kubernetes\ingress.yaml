apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kavya-ingress
  namespace: kavya-amr
  labels:
    app: kavya-ingress
    component: gateway
  annotations:
    # Ingress Controller
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    
    # SSL/TLS Configuration
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Security Headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Rate Limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # CORS Configuration
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://kavya-amr.org,https://app.kavya-amr.org"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    
    # Load Balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    nginx.ingress.kubernetes.io/load-balance: "round_robin"
    
    # Timeouts
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    
    # Body Size
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    
    # WebSocket Support
    nginx.ingress.kubernetes.io/proxy-set-headers: |
      X-Forwarded-For $proxy_add_x_forwarded_for;
      X-Forwarded-Proto $scheme;
      X-Real-IP $remote_addr;
      Host $host;
    
    # WAF Protection
    nginx.ingress.kubernetes.io/modsecurity-transaction-id: "$request_id"
    nginx.ingress.kubernetes.io/modsecurity-snippet: |
      SecRuleEngine On
      SecRequestBodyAccess On
      SecRule REQUEST_HEADERS:Content-Type "text/xml" \
        "id:'200001',phase:1,t:none,t:lowercase,pass,nolog,ctl:requestBodyProcessor=XML"
spec:
  tls:
  - hosts:
    - kavya-amr.org
    - app.kavya-amr.org
    - api.kavya-amr.org
    secretName: kavya-tls-secret
  rules:
  # Main Application
  - host: kavya-amr.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: kavya-frontend-service
            port:
              number: 80
  
  # Application Interface
  - host: app.kavya-amr.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: kavya-frontend-service
            port:
              number: 80
  
  # API Gateway
  - host: api.kavya-amr.org
    http:
      paths:
      # Backend API
      - path: /api/v1
        pathType: Prefix
        backend:
          service:
            name: kavya-backend-service
            port:
              number: 80
      
      # AI Models API
      - path: /ai
        pathType: Prefix
        backend:
          service:
            name: kavya-ai-models-service
            port:
              number: 80
      
      # Health Checks
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: kavya-backend-service
            port:
              number: 80
      
      # Metrics (Protected)
      - path: /metrics
        pathType: Prefix
        backend:
          service:
            name: kavya-prometheus-service
            port:
              number: 9090
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kavya-monitoring-ingress
  namespace: kavya-amr
  labels:
    app: kavya-monitoring
    component: observability
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    
    # Authentication for monitoring endpoints
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: kavya-monitoring-auth
    nginx.ingress.kubernetes.io/auth-realm: "KAVYA Monitoring - Authentication Required"
    
    # IP Whitelisting for monitoring
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
spec:
  tls:
  - hosts:
    - monitoring.kavya-amr.org
    - grafana.kavya-amr.org
    - prometheus.kavya-amr.org
    secretName: kavya-monitoring-tls-secret
  rules:
  # Grafana Dashboard
  - host: grafana.kavya-amr.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: kavya-grafana-service
            port:
              number: 3000
  
  # Prometheus Metrics
  - host: prometheus.kavya-amr.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: kavya-prometheus-service
            port:
              number: 9090
  
  # Monitoring Dashboard
  - host: monitoring.kavya-amr.org
    http:
      paths:
      - path: /grafana
        pathType: Prefix
        backend:
          service:
            name: kavya-grafana-service
            port:
              number: 3000
      - path: /prometheus
        pathType: Prefix
        backend:
          service:
            name: kavya-prometheus-service
            port:
              number: 9090
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
spec:
  acme:
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-staging
    solvers:
    - http01:
        ingress:
          class: nginx
---
apiVersion: v1
kind: Secret
metadata:
  name: kavya-monitoring-auth
  namespace: kavya-amr
type: Opaque
data:
  auth: YWRtaW46JGFwcjEkSDY1dnVhNzAkLnRiTXhPbGRBWVZjVUVPWnNzOGJWLw==  # admin:kavya_monitoring_2024
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: kavya-ingress-network-policy
  namespace: kavya-amr
spec:
  podSelector:
    matchLabels:
      app: nginx-ingress
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: kavya-amr
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 8001
    - protocol: TCP
      port: 9090
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-ingress-nginx
  namespace: kavya-amr
  labels:
    app: nginx-ingress
    component: controller
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:region:account:certificate/cert-id"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
spec:
  type: LoadBalancer
  externalTrafficPolicy: Local
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: https
    port: 443
    targetPort: 443
    protocol: TCP
  selector:
    app: nginx-ingress
    component: controller
