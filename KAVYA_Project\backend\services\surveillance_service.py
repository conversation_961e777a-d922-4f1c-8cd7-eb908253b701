"""
KAVYA AMR System - Surveillance Service
Global AMR surveillance and outbreak detection service
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np
import aiohttp
from collections import defaultdict

from ..core.config import get_settings
from ..core.security import anonymize_patient_data

logger = logging.getLogger(__name__)
settings = get_settings()


class SurveillanceService:
    """Service for global AMR surveillance and outbreak detection"""
    
    def __init__(self):
        self.global_network_url = settings.GLOBAL_SURVEILLANCE_URL
        self.surveillance_data = defaultdict(list)
        self.outbreak_thresholds = {
            "resistance_increase": 0.2,  # 20% increase
            "case_increase": 0.3,        # 30% increase
            "geographic_spread": 3,      # 3+ regions
            "temporal_clustering": 7     # within 7 days
        }
        
    async def validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate surveillance data quality and completeness"""
        try:
            logger.info(f"Validating surveillance data from {data.get('institution', 'Unknown')}")
            
            validated_data = data.copy()
            validation_score = 1.0
            issues = []
            
            # Check required fields
            required_fields = ["institution", "country", "reporting_period", "pathogen_data", "resistance_data"]
            for field in required_fields:
                if field not in data or not data[field]:
                    validation_score -= 0.2
                    issues.append(f"Missing or empty field: {field}")
            
            # Validate pathogen data
            if "pathogen_data" in data:
                for pathogen_entry in data["pathogen_data"]:
                    if not self._validate_pathogen_entry(pathogen_entry):
                        validation_score -= 0.1
                        issues.append(f"Invalid pathogen entry: {pathogen_entry}")
            
            # Validate resistance data
            if "resistance_data" in data:
                for resistance_entry in data["resistance_data"]:
                    if not self._validate_resistance_entry(resistance_entry):
                        validation_score -= 0.1
                        issues.append(f"Invalid resistance entry: {resistance_entry}")
            
            # Check data freshness
            reporting_period = data.get("reporting_period", "")
            if reporting_period:
                try:
                    period_date = datetime.fromisoformat(reporting_period.replace("Q", "-"))
                    days_old = (datetime.utcnow() - period_date).days
                    if days_old > 90:  # Data older than 3 months
                        validation_score -= 0.1
                        issues.append("Data is older than 3 months")
                except:
                    validation_score -= 0.1
                    issues.append("Invalid reporting period format")
            
            validated_data["data_quality_score"] = max(validation_score, 0.0)
            validated_data["validation_issues"] = issues
            
            logger.info(f"Data validation completed with score: {validation_score:.2f}")
            return validated_data
            
        except Exception as e:
            logger.error(f"Data validation failed: {str(e)}")
            raise
    
    async def anonymize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Anonymize surveillance data for privacy protection"""
        try:
            logger.info("Anonymizing surveillance data")
            
            anonymized_data = data.copy()
            
            # Anonymize pathogen data
            if "pathogen_data" in anonymized_data:
                for entry in anonymized_data["pathogen_data"]:
                    if "patient_data" in entry:
                        entry["patient_data"] = anonymize_patient_data(entry["patient_data"])
            
            # Anonymize resistance data
            if "resistance_data" in anonymized_data:
                for entry in anonymized_data["resistance_data"]:
                    if "patient_data" in entry:
                        entry["patient_data"] = anonymize_patient_data(entry["patient_data"])
            
            # Remove institution-specific identifiers
            if "institution_id" in anonymized_data:
                anonymized_data["institution_id"] = f"ANON_{hash(anonymized_data['institution_id']) % 10000}"
            
            # Add anonymization metadata
            anonymized_data["anonymization_applied"] = True
            anonymized_data["anonymization_timestamp"] = datetime.utcnow().isoformat()
            
            logger.info("Data anonymization completed")
            return anonymized_data
            
        except Exception as e:
            logger.error(f"Data anonymization failed: {str(e)}")
            raise
    
    async def store_data(self, data: Dict[str, Any], user_id: str) -> str:
        """Store surveillance data in local database"""
        try:
            surveillance_id = str(uuid.uuid4())
            
            # Add metadata
            data_record = {
                "surveillance_id": surveillance_id,
                "submitted_by": user_id,
                "submission_timestamp": datetime.utcnow().isoformat(),
                "data": data
            }
            
            # Store in memory (in production, use database)
            country = data.get("country", "unknown")
            self.surveillance_data[country].append(data_record)
            
            logger.info(f"Surveillance data stored with ID: {surveillance_id}")
            return surveillance_id
            
        except Exception as e:
            logger.error(f"Data storage failed: {str(e)}")
            raise
    
    async def share_with_global_network(self, data: Dict[str, Any], surveillance_id: str):
        """Share anonymized data with global surveillance network"""
        try:
            logger.info(f"Sharing surveillance data {surveillance_id} with global network")
            
            if not self.global_network_url:
                logger.info("Global network not configured, skipping share")
                return
            
            # Prepare data for sharing
            shared_data = {
                "surveillance_id": surveillance_id,
                "timestamp": datetime.utcnow().isoformat(),
                "data": data
            }
            
            # Send to global network
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.global_network_url}/submit",
                    json=shared_data,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        logger.info("Data successfully shared with global network")
                    else:
                        logger.warning(f"Global network sharing failed: {response.status}")
            
        except Exception as e:
            logger.warning(f"Global network sharing failed: {str(e)}")
    
    async def analyze_outbreak_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze data for outbreak patterns and risk assessment"""
        try:
            logger.info("Analyzing outbreak patterns")
            
            analysis_result = {
                "risk_score": 0.0,
                "trends": {},
                "recommendations": [],
                "global_context": {},
                "outbreak_indicators": []
            }
            
            # Analyze resistance trends
            resistance_trends = self._analyze_resistance_trends(data)
            analysis_result["trends"]["resistance"] = resistance_trends
            
            # Analyze case trends
            case_trends = self._analyze_case_trends(data)
            analysis_result["trends"]["cases"] = case_trends
            
            # Calculate risk score
            risk_score = self._calculate_outbreak_risk(resistance_trends, case_trends)
            analysis_result["risk_score"] = risk_score
            
            # Generate recommendations
            recommendations = self._generate_recommendations(risk_score, resistance_trends, case_trends)
            analysis_result["recommendations"] = recommendations
            
            # Get global context
            global_context = await self._get_global_context(data)
            analysis_result["global_context"] = global_context
            
            # Identify outbreak indicators
            outbreak_indicators = self._identify_outbreak_indicators(data, risk_score)
            analysis_result["outbreak_indicators"] = outbreak_indicators
            
            logger.info(f"Outbreak analysis completed with risk score: {risk_score:.2f}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Outbreak analysis failed: {str(e)}")
            raise
    
    async def get_global_trends(
        self,
        region: Optional[str] = None,
        pathogen: Optional[str] = None,
        time_range: str = "30d"
    ) -> Dict[str, Any]:
        """Get global AMR trends and predictions"""
        try:
            logger.info(f"Retrieving global trends for region={region}, pathogen={pathogen}")
            
            # Parse time range
            days = self._parse_time_range(time_range)
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            # Aggregate data
            aggregated_data = self._aggregate_surveillance_data(region, pathogen, cutoff_date)
            
            # Calculate trends
            trends = {
                "resistance_trends": self._calculate_resistance_trends(aggregated_data),
                "outbreak_predictions": self._predict_outbreaks(aggregated_data),
                "geographic_distribution": self._analyze_geographic_distribution(aggregated_data),
                "temporal_patterns": self._analyze_temporal_patterns(aggregated_data),
                "risk_assessment": self._assess_global_risk(aggregated_data),
                "last_updated": datetime.utcnow().isoformat()
            }
            
            logger.info("Global trends analysis completed")
            return trends
            
        except Exception as e:
            logger.error(f"Global trends analysis failed: {str(e)}")
            raise
    
    def _validate_pathogen_entry(self, entry: Dict[str, Any]) -> bool:
        """Validate individual pathogen entry"""
        required_fields = ["pathogen_name", "isolation_count", "sample_type"]
        return all(field in entry for field in required_fields)
    
    def _validate_resistance_entry(self, entry: Dict[str, Any]) -> bool:
        """Validate individual resistance entry"""
        required_fields = ["pathogen", "antibiotic", "resistance_status"]
        return all(field in entry for field in required_fields)
    
    def _analyze_resistance_trends(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze resistance trends in the data"""
        trends = {
            "increasing_resistance": [],
            "emerging_mechanisms": [],
            "geographic_spread": [],
            "temporal_changes": {}
        }
        
        # Analyze resistance data
        resistance_data = data.get("resistance_data", [])
        
        # Group by pathogen and antibiotic
        resistance_by_pathogen = defaultdict(lambda: defaultdict(list))
        for entry in resistance_data:
            pathogen = entry.get("pathogen", "unknown")
            antibiotic = entry.get("antibiotic", "unknown")
            status = entry.get("resistance_status", "unknown")
            resistance_by_pathogen[pathogen][antibiotic].append(status)
        
        # Calculate resistance rates
        for pathogen, antibiotics in resistance_by_pathogen.items():
            for antibiotic, statuses in antibiotics.items():
                resistant_count = sum(1 for s in statuses if s.lower() == "resistant")
                total_count = len(statuses)
                resistance_rate = resistant_count / total_count if total_count > 0 else 0
                
                if resistance_rate > 0.5:  # >50% resistance
                    trends["increasing_resistance"].append({
                        "pathogen": pathogen,
                        "antibiotic": antibiotic,
                        "resistance_rate": resistance_rate,
                        "sample_size": total_count
                    })
        
        return trends
    
    def _analyze_case_trends(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze case trends in the data"""
        trends = {
            "case_counts": {},
            "geographic_distribution": {},
            "temporal_patterns": {},
            "severity_trends": {}
        }
        
        # Analyze pathogen data
        pathogen_data = data.get("pathogen_data", [])
        
        # Count cases by pathogen
        pathogen_counts = defaultdict(int)
        for entry in pathogen_data:
            pathogen = entry.get("pathogen_name", "unknown")
            count = entry.get("isolation_count", 1)
            pathogen_counts[pathogen] += count
        
        trends["case_counts"] = dict(pathogen_counts)
        
        return trends
    
    def _calculate_outbreak_risk(self, resistance_trends: Dict, case_trends: Dict) -> float:
        """Calculate outbreak risk score"""
        risk_score = 0.0
        
        # Risk from increasing resistance
        increasing_resistance = resistance_trends.get("increasing_resistance", [])
        if increasing_resistance:
            avg_resistance_rate = np.mean([r["resistance_rate"] for r in increasing_resistance])
            risk_score += avg_resistance_rate * 0.4
        
        # Risk from case counts
        case_counts = case_trends.get("case_counts", {})
        if case_counts:
            total_cases = sum(case_counts.values())
            if total_cases > 100:  # Threshold for concern
                risk_score += min(total_cases / 1000, 0.3)
        
        # Risk from multiple resistant pathogens
        resistant_pathogens = len(increasing_resistance)
        if resistant_pathogens > 3:
            risk_score += 0.2
        
        return min(risk_score, 1.0)
    
    def _generate_recommendations(self, risk_score: float, resistance_trends: Dict, case_trends: Dict) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        if risk_score > 0.7:
            recommendations.append("HIGH ALERT: Implement enhanced infection control measures")
            recommendations.append("Consider outbreak investigation")
            recommendations.append("Notify public health authorities")
        elif risk_score > 0.5:
            recommendations.append("MODERATE ALERT: Increase surveillance frequency")
            recommendations.append("Review antibiotic stewardship protocols")
        elif risk_score > 0.3:
            recommendations.append("Monitor trends closely")
            recommendations.append("Ensure proper infection control practices")
        else:
            recommendations.append("Continue routine surveillance")
        
        # Specific recommendations based on resistance trends
        increasing_resistance = resistance_trends.get("increasing_resistance", [])
        for resistance in increasing_resistance:
            if resistance["resistance_rate"] > 0.8:
                recommendations.append(
                    f"Critical resistance detected: {resistance['pathogen']} to {resistance['antibiotic']}"
                )
        
        return recommendations
    
    async def _get_global_context(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get global context for the surveillance data"""
        try:
            if not self.global_network_url:
                return {"status": "global_network_not_configured"}
            
            # Query global network for context
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.global_network_url}/context",
                    params={
                        "country": data.get("country", ""),
                        "region": data.get("region", "")
                    },
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"status": "global_context_unavailable"}
        except:
            return {"status": "global_context_error"}
    
    def _identify_outbreak_indicators(self, data: Dict[str, Any], risk_score: float) -> List[str]:
        """Identify specific outbreak indicators"""
        indicators = []
        
        if risk_score > 0.7:
            indicators.append("High resistance rates detected")
        
        # Check for rapid increase in cases
        case_counts = data.get("pathogen_data", [])
        total_cases = sum(entry.get("isolation_count", 0) for entry in case_counts)
        if total_cases > 50:
            indicators.append("Elevated case counts")
        
        # Check for multi-drug resistance
        resistance_data = data.get("resistance_data", [])
        mdr_count = 0
        for entry in resistance_data:
            if entry.get("resistance_status", "").lower() == "resistant":
                mdr_count += 1
        
        if mdr_count > 10:
            indicators.append("Multiple drug resistance detected")
        
        return indicators
    
    def _parse_time_range(self, time_range: str) -> int:
        """Parse time range string to days"""
        if time_range.endswith("d"):
            return int(time_range[:-1])
        elif time_range.endswith("w"):
            return int(time_range[:-1]) * 7
        elif time_range.endswith("m"):
            return int(time_range[:-1]) * 30
        else:
            return 30  # Default to 30 days
    
    def _aggregate_surveillance_data(self, region: Optional[str], pathogen: Optional[str], cutoff_date: datetime) -> List[Dict]:
        """Aggregate surveillance data based on filters"""
        aggregated = []
        
        for country_data in self.surveillance_data.values():
            for record in country_data:
                submission_date = datetime.fromisoformat(record["submission_timestamp"])
                if submission_date >= cutoff_date:
                    data = record["data"]
                    
                    # Apply filters
                    if region and data.get("region") != region:
                        continue
                    
                    # Filter by pathogen if specified
                    if pathogen:
                        pathogen_data = data.get("pathogen_data", [])
                        filtered_pathogen_data = [
                            p for p in pathogen_data 
                            if pathogen.lower() in p.get("pathogen_name", "").lower()
                        ]
                        if not filtered_pathogen_data:
                            continue
                    
                    aggregated.append(data)
        
        return aggregated
    
    def _calculate_resistance_trends(self, data: List[Dict]) -> Dict[str, Any]:
        """Calculate resistance trends from aggregated data"""
        # Simplified trend calculation
        return {
            "overall_trend": "increasing",
            "trend_strength": 0.15,
            "confidence": 0.8
        }
    
    def _predict_outbreaks(self, data: List[Dict]) -> Dict[str, Any]:
        """Predict potential outbreaks"""
        return {
            "outbreak_probability": 0.3,
            "predicted_regions": ["Region A", "Region B"],
            "time_horizon": "30 days",
            "confidence": 0.7
        }
    
    def _analyze_geographic_distribution(self, data: List[Dict]) -> Dict[str, Any]:
        """Analyze geographic distribution of resistance"""
        return {
            "hotspots": ["Country A", "Country B"],
            "spread_pattern": "clustered",
            "geographic_risk": 0.6
        }
    
    def _analyze_temporal_patterns(self, data: List[Dict]) -> Dict[str, Any]:
        """Analyze temporal patterns in the data"""
        return {
            "seasonal_pattern": "winter_peak",
            "trend_direction": "increasing",
            "acceleration": 0.1
        }
    
    def _assess_global_risk(self, data: List[Dict]) -> Dict[str, Any]:
        """Assess global risk level"""
        return {
            "risk_level": "moderate",
            "risk_score": 0.5,
            "contributing_factors": ["increasing_resistance", "geographic_spread"]
        }
    
    async def health_check(self) -> Dict[str, str]:
        """Health check for surveillance service"""
        try:
            data_count = sum(len(records) for records in self.surveillance_data.values())
            
            # Check global network connectivity
            network_status = "not_configured"
            if self.global_network_url:
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            f"{self.global_network_url}/health",
                            timeout=aiohttp.ClientTimeout(total=5)
                        ) as response:
                            network_status = "healthy" if response.status == 200 else "unhealthy"
                except:
                    network_status = "unreachable"
            
            return {
                "status": "healthy",
                "surveillance_records": str(data_count),
                "global_network": network_status,
                "countries_monitored": str(len(self.surveillance_data))
            }
            
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
