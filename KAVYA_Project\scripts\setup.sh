#!/bin/bash

# KAVYA AMR System - Complete Setup Script
# This script sets up the entire KAVYA system from scratch

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Banner
echo -e "${CYAN}"
cat << "EOF"
 ██╗  ██╗ █████╗ ██╗   ██╗██╗   ██╗ █████╗ 
 ██║ ██╔╝██╔══██╗██║   ██║╚██╗ ██╔╝██╔══██╗
 █████╔╝ ███████║██║   ██║ ╚████╔╝ ███████║
 ██╔═██╗ ██╔══██║╚██╗ ██╔╝  ╚██╔╝  ██╔══██║
 ██║  ██╗██║  ██║ ╚████╔╝    ██║   ██║  ██║
 ╚═╝  ╚═╝╚═╝  ╚═╝  ╚═══╝     ╚═╝   ╚═╝  ╚═╝
                                            
 AI-Powered Antimicrobial Resistance System
EOF
echo -e "${NC}"

log "🧬 Starting KAVYA AMR System Setup..."

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   error "This script should not be run as root for security reasons"
fi

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    DISTRO=$(lsb_release -si 2>/dev/null || echo "Unknown")
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    OS="windows"
else
    error "Unsupported operating system: $OSTYPE"
fi

log "Detected OS: $OS"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install dependencies based on OS
install_dependencies() {
    log "📦 Installing system dependencies..."
    
    case $OS in
        "linux")
            if command_exists apt-get; then
                sudo apt-get update
                sudo apt-get install -y \
                    python3 python3-pip python3-venv \
                    nodejs npm \
                    docker.io docker-compose \
                    git curl wget \
                    build-essential \
                    postgresql-client \
                    mongodb-clients \
                    redis-tools
            elif command_exists yum; then
                sudo yum update -y
                sudo yum install -y \
                    python3 python3-pip \
                    nodejs npm \
                    docker docker-compose \
                    git curl wget \
                    gcc gcc-c++ make \
                    postgresql \
                    mongodb-org-tools \
                    redis
            else
                error "Unsupported Linux distribution"
            fi
            ;;
        "macos")
            if ! command_exists brew; then
                log "Installing Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            brew update
            brew install \
                python@3.11 \
                node \
                docker \
                docker-compose \
                git \
                postgresql \
                mongodb/brew/mongodb-community \
                redis
            ;;
        "windows")
            warn "Windows detected. Please ensure you have the following installed:"
            warn "- Python 3.9+"
            warn "- Node.js 18+"
            warn "- Docker Desktop"
            warn "- Git"
            warn "- PostgreSQL"
            warn "- MongoDB"
            warn "- Redis"
            ;;
    esac
}

# Function to setup Python environment
setup_python_env() {
    log "🐍 Setting up Python environment..."
    
    # Check Python version
    if ! command_exists python3; then
        error "Python 3 is not installed"
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$PYTHON_VERSION < 3.9" | bc -l) -eq 1 ]]; then
        error "Python 3.9+ is required, found $PYTHON_VERSION"
    fi
    
    # Create virtual environment
    if [[ ! -d "venv" ]]; then
        log "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate || source venv/Scripts/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install Python dependencies
    log "Installing Python dependencies..."
    pip install -r requirements.txt
    
    log "✅ Python environment setup complete"
}

# Function to setup Node.js environment
setup_nodejs_env() {
    log "📦 Setting up Node.js environment..."
    
    # Check Node.js version
    if ! command_exists node; then
        error "Node.js is not installed"
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $NODE_VERSION -lt 18 ]]; then
        error "Node.js 18+ is required, found v$NODE_VERSION"
    fi
    
    # Install frontend dependencies
    cd frontend
    log "Installing frontend dependencies..."
    npm install
    cd ..
    
    log "✅ Node.js environment setup complete"
}

# Function to setup databases
setup_databases() {
    log "🗄️ Setting up databases..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running. Please start Docker and try again."
    fi
    
    # Start databases with Docker Compose
    log "Starting database services..."
    docker-compose up -d postgres mongodb redis
    
    # Wait for databases to be ready
    log "Waiting for databases to be ready..."
    sleep 30
    
    # Run database migrations
    log "Running database migrations..."
    source venv/bin/activate || source venv/Scripts/activate
    cd backend
    python -c "
from models.database import create_tables
from sqlalchemy import create_engine
import os

database_url = os.getenv('DATABASE_URL', 'postgresql://kavya_user:kavya_password@localhost:5432/kavya_amr')
engine = create_engine(database_url)
create_tables(engine)
print('✅ Database tables created successfully')
"
    cd ..
    
    log "✅ Database setup complete"
}

# Function to download and setup AI models
setup_ai_models() {
    log "🧠 Setting up AI models..."
    
    # Create models directory
    mkdir -p models
    
    # Download pre-trained models (placeholder URLs)
    log "Downloading pre-trained models..."
    
    # Pathogen classification model
    if [[ ! -f "models/pathogen_classifier.pth" ]]; then
        log "Downloading pathogen classification model..."
        # wget -O models/pathogen_classifier.pth "https://models.kavya-amr.org/pathogen_classifier.pth"
        # For now, create placeholder
        touch models/pathogen_classifier.pth
    fi
    
    # Genomic transformer model
    if [[ ! -f "models/genomic_transformer.pth" ]]; then
        log "Downloading genomic transformer model..."
        # wget -O models/genomic_transformer.pth "https://models.kavya-amr.org/genomic_transformer.pth"
        touch models/genomic_transformer.pth
    fi
    
    # Multi-modal fusion model
    if [[ ! -f "models/multimodal_fusion.pth" ]]; then
        log "Downloading multi-modal fusion model..."
        # wget -O models/multimodal_fusion.pth "https://models.kavya-amr.org/multimodal_fusion.pth"
        touch models/multimodal_fusion.pth
    fi
    
    # Quantum predictor model
    if [[ ! -f "models/quantum_predictor.pkl" ]]; then
        log "Downloading quantum predictor model..."
        # wget -O models/quantum_predictor.pkl "https://models.kavya-amr.org/quantum_predictor.pkl"
        touch models/quantum_predictor.pkl
    fi
    
    log "✅ AI models setup complete"
}

# Function to setup sample data
setup_sample_data() {
    log "📊 Setting up sample data..."
    
    # Create data directories
    mkdir -p data/{raw,processed,genomics,images,clinical}
    
    # Download sample datasets (placeholder)
    log "Downloading sample datasets..."
    
    # Sample microscopy images
    if [[ ! -d "data/images/samples" ]]; then
        mkdir -p data/images/samples
        log "Sample microscopy images would be downloaded here"
        # wget -O data/images/sample_images.zip "https://data.kavya-amr.org/sample_images.zip"
        # unzip data/images/sample_images.zip -d data/images/samples/
    fi
    
    # Sample genomic data
    if [[ ! -d "data/genomics/samples" ]]; then
        mkdir -p data/genomics/samples
        log "Sample genomic data would be downloaded here"
        # wget -O data/genomics/sample_genomes.zip "https://data.kavya-amr.org/sample_genomes.zip"
        # unzip data/genomics/sample_genomes.zip -d data/genomics/samples/
    fi
    
    log "✅ Sample data setup complete"
}

# Function to run tests
run_tests() {
    log "🧪 Running tests..."
    
    # Python tests
    source venv/bin/activate || source venv/Scripts/activate
    cd backend
    python -m pytest tests/ -v --cov=. --cov-report=html
    cd ..
    
    # Frontend tests
    cd frontend
    npm test -- --coverage --watchAll=false
    cd ..
    
    log "✅ Tests completed"
}

# Function to start services
start_services() {
    log "🚀 Starting KAVYA services..."
    
    # Start all services with Docker Compose
    docker-compose up -d
    
    # Wait for services to be ready
    log "Waiting for services to start..."
    sleep 60
    
    # Health checks
    log "Performing health checks..."
    
    # Check backend
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log "✅ Backend service is healthy"
    else
        warn "❌ Backend service health check failed"
    fi
    
    # Check frontend
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        log "✅ Frontend service is healthy"
    else
        warn "❌ Frontend service health check failed"
    fi
    
    # Check AI models
    if curl -f http://localhost:8001/health >/dev/null 2>&1; then
        log "✅ AI models service is healthy"
    else
        warn "❌ AI models service health check failed"
    fi
    
    log "✅ Services started successfully"
}

# Function to display final information
display_final_info() {
    echo -e "${CYAN}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                    🎉 SETUP COMPLETE! 🎉                    ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    
    log "🌐 KAVYA AMR System is now running!"
    echo ""
    log "📱 Access Points:"
    log "   • Frontend:     http://localhost:3000"
    log "   • Backend API:  http://localhost:8000"
    log "   • API Docs:     http://localhost:8000/docs"
    log "   • AI Models:    http://localhost:8001"
    log "   • Grafana:      http://localhost:3001 (admin/kavya_admin)"
    log "   • Prometheus:   http://localhost:9090"
    echo ""
    log "📚 Documentation:"
    log "   • Architecture: ./docs/architecture/"
    log "   • API Guide:    http://localhost:8000/docs"
    log "   • User Manual:  ./docs/user_guide.md"
    echo ""
    log "🔧 Management Commands:"
    log "   • Stop services:    docker-compose down"
    log "   • View logs:        docker-compose logs -f"
    log "   • Restart:          docker-compose restart"
    log "   • Update:           git pull && ./scripts/setup.sh"
    echo ""
    log "🆘 Support:"
    log "   • Issues:    https://github.com/kavya-amr/kavya-system/issues"
    log "   • Email:     <EMAIL>"
    log "   • Docs:      https://docs.kavya-amr.org"
    echo ""
    warn "⚠️  Remember to:"
    warn "   • Configure your .env file with production settings"
    warn "   • Set up SSL certificates for production deployment"
    warn "   • Review security settings before clinical use"
    warn "   • Ensure compliance with local healthcare regulations"
}

# Main execution
main() {
    log "Starting KAVYA AMR System setup..."
    
    # Check if .env file exists
    if [[ ! -f ".env" ]]; then
        log "Creating .env file from template..."
        cp .env.example .env
        warn "Please review and update the .env file with your configuration"
    fi
    
    # Run setup steps
    install_dependencies
    setup_python_env
    setup_nodejs_env
    setup_databases
    setup_ai_models
    setup_sample_data
    
    # Optional: Run tests
    read -p "Do you want to run tests? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_tests
    fi
    
    # Start services
    start_services
    
    # Display final information
    display_final_info
    
    log "🎉 KAVYA AMR System setup completed successfully!"
}

# Run main function
main "$@"
