version: '3.8'

services:
  # =============================================================================
  # KAVYA Backend API
  # =============================================================================
  kavya-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kavya-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=****************************************************/kavya_amr
      - MONGODB_URL=*****************************************************************
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - mongodb
      - redis
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./models:/app/models
    networks:
      - kavya-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Frontend (Next.js)
  # =============================================================================
  kavya-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: kavya-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
    depends_on:
      - kavya-backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - kavya-network
    restart: unless-stopped

  # =============================================================================
  # PostgreSQL Database
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: kavya-postgres
    environment:
      - POSTGRES_DB=kavya_amr
      - POSTGRES_USER=kavya_user
      - POSTGRES_PASSWORD=kavya_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - kavya-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U kavya_user -d kavya_amr"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # MongoDB (Genomic Data)
  # =============================================================================
  mongodb:
    image: mongo:6.0
    container_name: kavya-mongodb
    environment:
      - MONGO_INITDB_ROOT_USERNAME=kavya_mongo
      - MONGO_INITDB_ROOT_PASSWORD=kavya_password
      - MONGO_INITDB_DATABASE=kavya_genomics
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/mongo-init:/docker-entrypoint-initdb.d
    networks:
      - kavya-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Redis (Caching & Sessions)
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: kavya-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - kavya-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Nginx (Reverse Proxy & Load Balancer)
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: kavya-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
    depends_on:
      - kavya-backend
      - kavya-frontend
    networks:
      - kavya-network
    restart: unless-stopped

  # =============================================================================
  # Monitoring & Observability
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: kavya-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - kavya-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: kavya-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=kavya_admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - kavya-network
    restart: unless-stopped

  # =============================================================================
  # Message Queue (Celery with RabbitMQ)
  # =============================================================================
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: kavya-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=kavya_rabbit
      - RABBITMQ_DEFAULT_PASS=kavya_password
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - kavya-network
    restart: unless-stopped

  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kavya-celery-worker
    command: celery -A app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=****************************************************/kavya_amr
      - CELERY_BROKER_URL=pyamqp://kavya_rabbit:kavya_password@rabbitmq:5672//
    depends_on:
      - postgres
      - rabbitmq
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./models:/app/models
    networks:
      - kavya-network
    restart: unless-stopped

  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kavya-celery-beat
    command: celery -A app.celery beat --loglevel=info
    environment:
      - DATABASE_URL=****************************************************/kavya_amr
      - CELERY_BROKER_URL=pyamqp://kavya_rabbit:kavya_password@rabbitmq:5672//
    depends_on:
      - postgres
      - rabbitmq
    volumes:
      - ./backend:/app
    networks:
      - kavya-network
    restart: unless-stopped

  # =============================================================================
  # AI Model Serving
  # =============================================================================
  model-server:
    build:
      context: ./ai_models
      dockerfile: Dockerfile
    container_name: kavya-model-server
    ports:
      - "8001:8001"
    environment:
      - MODEL_PATH=/models
      - DEVICE=cuda
    volumes:
      - ./models:/models
      - ./ai_models:/app
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - kavya-network
    restart: unless-stopped

  # =============================================================================
  # Jupyter Lab (Development)
  # =============================================================================
  jupyter:
    build:
      context: ./notebooks
      dockerfile: Dockerfile
    container_name: kavya-jupyter
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=kavya_jupyter_token
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./data:/home/<USER>/data
      - ./models:/home/<USER>/models
    networks:
      - kavya-network
    restart: unless-stopped

# =============================================================================
# Networks
# =============================================================================
networks:
  kavya-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  postgres_data:
    driver: local
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  rabbitmq_data:
    driver: local
