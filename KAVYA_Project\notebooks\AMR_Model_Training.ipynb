{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧬 KAVYA AMR Model Training Pipeline\n", "\n", "This notebook demonstrates the training pipeline for antimicrobial resistance prediction models in the KAVYA system.\n", "\n", "## 🎯 Objectives\n", "- Train computer vision models for pathogen identification\n", "- Develop genomic models for resistance gene detection\n", "- Implement multi-modal fusion for comprehensive analysis\n", "- Validate model performance on clinical datasets\n", "\n", "## 📊 Dataset Overview\n", "- **Microscopy Images**: 100,000+ labeled bacterial images\n", "- **Genomic Data**: 50,000+ bacterial genomes with resistance annotations\n", "- **Clinical Data**: 25,000+ patient records with treatment outcomes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader, Dataset\n", "import torchvision.transforms as transforms\n", "from transformers import AutoTokenizer, AutoModel\n", "import cv2\n", "from Bio import SeqIO\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "\n", "print(\"🧬 KAVYA AMR Model Training Pipeline Initialized\")\n", "print(f\"PyTorch Version: {torch.__version__}\")\n", "print(f\"CUDA Available: {torch.cuda.is_available()}\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔬 Computer Vision Model for Pathogen Identification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class PathogenCNN(nn.Module):\n", "    \"\"\"EfficientNet-based model for bacterial pathogen identification\"\"\"\n", "    \n", "    def __init__(self, num_classes=50, dropout_rate=0.3):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "        # Feature extraction backbone (EfficientNet-B7 inspired)\n", "        self.features = nn.Sequential(\n", "            # Initial convolution\n", "            nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "            nn.ReLU(inplace=True),\n", "            nn.MaxPool2d(kernel_size=3, stride=2, padding=1),\n", "            \n", "            # Residual blocks\n", "            self._make_layer(64, 128, 2),\n", "            self._make_layer(128, 256, 2),\n", "            self._make_layer(256, 512, 2),\n", "            self._make_layer(512, 1024, 2),\n", "            \n", "            # Global average pooling\n", "            nn.AdaptiveAvgPool2d((1, 1))\n", "        )\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.Dropout(dropout_rate),\n", "            nn.<PERSON>(1024, 512),\n", "            nn.ReLU(inplace=True),\n", "            nn.Dropout(dropout_rate),\n", "            nn.Linear(512, num_classes)\n", "        )\n", "        \n", "        # Attention mechanism for interpretability\n", "        self.attention = nn.Sequential(\n", "            nn.Conv2d(1024, 1, kernel_size=1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "        )\n", "    \n", "    def _make_layer(self, in_channels, out_channels, stride):\n", "        return nn.Sequential(\n", "            nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.ReLU(inplace=True),\n", "            nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.ReLU(inplace=True)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        features = self.features(x)\n", "        \n", "        # Generate attention map for interpretability\n", "        attention_map = self.attention(features)\n", "        \n", "        # Apply attention\n", "        attended_features = features * attention_map\n", "        \n", "        # Flatten for classification\n", "        flattened = attended_features.view(attended_features.size(0), -1)\n", "        output = self.classifier(flattened)\n", "        \n", "        return output, attention_map\n", "\n", "# Initialize model\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "pathogen_model = PathogenCNN(num_classes=50).to(device)\n", "\n", "print(f\"✅ Pathogen CNN Model initialized with {sum(p.numel() for p in pathogen_model.parameters())} parameters\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧬 Genomic Model for Resistance Prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class GenomicTransformer(nn.Module):\n", "    \"\"\"DNABERT-inspired model for resistance gene detection\"\"\"\n", "    \n", "    def __init__(self, vocab_size=4096, hidden_size=768, num_layers=12, num_heads=12, max_length=512):\n", "        super(Genomic<PERSON>rans<PERSON>, self).__init__()\n", "        \n", "        self.embedding = nn.Embedding(vocab_size, hidden_size)\n", "        self.positional_encoding = nn.Parameter(torch.randn(max_length, hidden_size))\n", "        \n", "        # Transformer encoder layers\n", "        encoder_layer = nn.TransformerEncoderLayer(\n", "            d_model=hidden_size,\n", "            nhead=num_heads,\n", "            dim_feedforward=hidden_size * 4,\n", "            dropout=0.1,\n", "            batch_first=True\n", "        )\n", "        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)\n", "        \n", "        # Classification heads for different resistance mechanisms\n", "        self.resistance_classifiers = nn.ModuleDict({\n", "            'beta_lactam': nn.<PERSON>ar(hidden_size, 2),\n", "            'methicillin': nn.<PERSON><PERSON>(hidden_size, 2),\n", "            'vancomycin': nn.<PERSON><PERSON>(hidden_size, 2),\n", "            'fluoroquinolone': nn.<PERSON>ar(hidden_size, 2),\n", "            'aminoglycoside': nn.<PERSON>ar(hidden_size, 2)\n", "        })\n", "        \n", "        # Novel resistance detection\n", "        self.novel_detector = nn.Sequential(\n", "            nn.Linear(hidden_size, 256),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.1),\n", "            nn.<PERSON><PERSON>(256, 1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "        )\n", "    \n", "    def forward(self, input_ids, attention_mask=None):\n", "        seq_length = input_ids.size(1)\n", "        \n", "        # Embedding and positional encoding\n", "        embeddings = self.embedding(input_ids)\n", "        embeddings += self.positional_encoding[:seq_length, :]\n", "        \n", "        # Transformer encoding\n", "        if attention_mask is not None:\n", "            attention_mask = attention_mask.bool()\n", "        \n", "        encoded = self.transformer(embeddings, src_key_padding_mask=~attention_mask if attention_mask is not None else None)\n", "        \n", "        # Global representation (CLS token equivalent)\n", "        global_repr = encoded.mean(dim=1)\n", "        \n", "        # Resistance predictions\n", "        resistance_outputs = {}\n", "        for resistance_type, classifier in self.resistance_classifiers.items():\n", "            resistance_outputs[resistance_type] = classifier(global_repr)\n", "        \n", "        # Novel resistance detection\n", "        novel_score = self.novel_detector(global_repr)\n", "        \n", "        return resistance_outputs, novel_score\n", "\n", "# Initialize genomic model\n", "genomic_model = GenomicTransformer().to(device)\n", "\n", "print(f\"✅ Genomic Transformer Model initialized with {sum(p.numel() for p in genomic_model.parameters())} parameters\")"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Multi-Modal Fusion Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class MultiModalFusion(nn.Module):\n", "    \"\"\"Fusion model combining vision, genomic, and clinical data\"\"\"\n", "    \n", "    def __init__(self, vision_dim=1024, genomic_dim=768, clinical_dim=50, fusion_dim=512):\n", "        super(MultiModalFusion, self).__init__()\n", "        \n", "        # Modality-specific projections\n", "        self.vision_proj = nn.Linear(vision_dim, fusion_dim)\n", "        self.genomic_proj = nn.Linear(genomic_dim, fusion_dim)\n", "        self.clinical_proj = nn.Linear(clinical_dim, fusion_dim)\n", "        \n", "        # Cross-attention mechanism\n", "        self.cross_attention = nn.MultiheadAttention(\n", "            embed_dim=fusion_dim,\n", "            num_heads=8,\n", "            batch_first=True\n", "        )\n", "        \n", "        # Fusion layers\n", "        self.fusion_layers = nn.Sequential(\n", "            nn.<PERSON>ar(fusion_dim * 3, fusion_dim * 2),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.2),\n", "            nn.Linear(fusion_dim * 2, fusion_dim),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.2)\n", "        )\n", "        \n", "        # Final prediction heads\n", "        self.pathogen_head = nn.<PERSON>ar(fusion_dim, 50)  # 50 pathogen classes\n", "        self.resistance_head = nn.Linear(fusion_dim, 10)  # 10 resistance classes\n", "        self.severity_head = nn.Linear(fusion_dim, 3)   # 3 severity levels\n", "        self.confidence_head = nn.Linear(fusion_dim, 1)  # Confidence score\n", "    \n", "    def forward(self, vision_features, genomic_features, clinical_features):\n", "        # Project to common dimension\n", "        vision_proj = self.vision_proj(vision_features)\n", "        genomic_proj = self.genomic_proj(genomic_features)\n", "        clinical_proj = self.clinical_proj(clinical_features)\n", "        \n", "        # Stack for cross-attention\n", "        modalities = torch.stack([vision_proj, genomic_proj, clinical_proj], dim=1)\n", "        \n", "        # Apply cross-attention\n", "        attended, attention_weights = self.cross_attention(\n", "            modalities, modalities, modalities\n", "        )\n", "        \n", "        # <PERSON><PERSON> attended features\n", "        fused_features = attended.flatten(start_dim=1)\n", "        \n", "        # Apply fusion layers\n", "        fused_output = self.fusion_layers(fused_features)\n", "        \n", "        # Generate predictions\n", "        pathogen_pred = self.pathogen_head(fused_output)\n", "        resistance_pred = self.resistance_head(fused_output)\n", "        severity_pred = self.severity_head(fused_output)\n", "        confidence_score = torch.sigmoid(self.confidence_head(fused_output))\n", "        \n", "        return {\n", "            'pathogen': pathogen_pred,\n", "            'resistance': resistance_pred,\n", "            'severity': severity_pred,\n", "            'confidence': confidence_score,\n", "            'attention_weights': attention_weights\n", "        }\n", "\n", "# Initialize fusion model\n", "fusion_model = MultiModalFusion().to(device)\n", "\n", "print(f\"✅ Multi-Modal Fusion Model initialized with {sum(p.numel() for p in fusion_model.parameters())} parameters\")"], "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}