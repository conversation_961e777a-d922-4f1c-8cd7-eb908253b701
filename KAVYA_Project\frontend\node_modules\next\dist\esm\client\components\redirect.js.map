{"version": 3, "sources": ["../../../src/client/components/redirect.ts"], "names": ["requestAsyncStorage", "actionAsyncStorage", "RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "requestStore", "getStore", "mutableCookies", "redirect", "actionStore", "isAction", "<PERSON><PERSON><PERSON>", "permanentRedirect", "PermanentRedirect", "isRedirectError", "errorCode", "destination", "status", "split", "Number", "isNaN", "getURLFromRedirectError", "getRedirectTypeFromError", "getRedirectStatusCodeFromError"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAkC;AAEtE,SAASC,kBAAkB,QAAQ,kCAAiC;AACpE,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,MAAMC,sBAAsB;;UAEhBC;;;GAAAA,iBAAAA;AAUZ,OAAO,SAASC,iBACdC,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,uBAAAA,aAAiCN,mBAAmBO,iBAAiB;IAErE,MAAMC,QAAQ,IAAIC,MAAMR;IACxBO,MAAME,MAAM,GAAG,AAAGT,sBAAoB,MAAGI,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,MAAMK,eAAeb,oBAAoBc,QAAQ;IACjD,IAAID,cAAc;QAChBH,MAAMK,cAAc,GAAGF,aAAaE,cAAc;IACpD;IACA,OAAOL;AACT;AAEA;;;;;;;;;;CAUC,GACD,OAAO,SAASM,SACd,2BAA2B,GAC3BV,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA;IAEA,MAAMU,cAAchB,mBAAmBa,QAAQ;IAC/C,MAAMT,iBACJC,KACAC,MACA,uDAAuD;IACvD,4DAA4D;IAC5D,kDAAkD;IAClDU,CAAAA,+BAAAA,YAAaC,QAAQ,IACjBhB,mBAAmBiB,QAAQ,GAC3BjB,mBAAmBO,iBAAiB;AAE5C;AAEA;;;;;;;;;;CAUC,GACD,OAAO,SAASW,kBACd,2BAA2B,GAC3Bd,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA;IAEA,MAAMU,cAAchB,mBAAmBa,QAAQ;IAC/C,MAAMT,iBACJC,KACAC,MACA,uDAAuD;IACvD,4DAA4D;IAC5D,kDAAkD;IAClDU,CAAAA,+BAAAA,YAAaC,QAAQ,IACjBhB,mBAAmBiB,QAAQ,GAC3BjB,mBAAmBmB,iBAAiB;AAE5C;AAEA;;;;;;CAMC,GACD,OAAO,SAASC,gBACdZ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAME,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAM,CAACW,WAAWhB,MAAMiB,aAAaC,OAAO,GAAGf,MAAME,MAAM,CAACc,KAAK,CAAC,KAAK;IAEvE,MAAMlB,aAAamB,OAAOF;IAE1B,OACEF,cAAcpB,uBACbI,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOiB,gBAAgB,YACvB,CAACI,MAAMpB,eACPA,cAAcN;AAElB;AAYA,OAAO,SAAS2B,wBAAwBnB,KAAc;IACpD,IAAI,CAACY,gBAAgBZ,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACc,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEA,OAAO,SAASI,yBACdpB,KAAuB;IAEvB,IAAI,CAACY,gBAAgBZ,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD,MAAME,MAAM,CAACc,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEA,OAAO,SAASK,+BACdrB,KAAuB;IAEvB,IAAI,CAACY,gBAAgBZ,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOgB,OAAOjB,MAAME,MAAM,CAACc,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AAC7C"}