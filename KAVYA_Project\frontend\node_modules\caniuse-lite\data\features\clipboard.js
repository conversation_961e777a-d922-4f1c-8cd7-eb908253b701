module.exports={A:{A:{"2436":"K D E F A B pC"},B:{"260":"O P","2436":"C L M G N","8196":"0 9 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB I"},C:{"2":"1 2 qC NC J RB K D E F A B C L M G N O P SB tC uC","772":"3 4 5 6 7 8 TB UB VB WB XB YB ZB aB bB cB dB eB fB","4100":"0 9 gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB OC yB PC zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R QC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB I GC RC SC TC rC sC"},D:{"2":"J RB K D E F A B C","2564":"1 2 3 4 5 6 7 8 L M G N O P SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB","8196":"0 9 xB OC yB PC zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB I GC RC SC TC","10244":"iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB"},E:{"1":"C L M G IC 0C 1C 2C WC XC JC 3C KC YC ZC aC bC cC 4C LC dC eC fC gC hC 5C MC iC jC kC lC mC 6C","16":"vC UC","2308":"A B VC HC","2820":"J RB K D E F wC xC yC zC"},F:{"2":"F B 7C 8C 9C AD HC nC BD","16":"C","516":"IC","2564":"1 2 3 4 5 6 7 8 G N O P SB TB UB","8196":"0 kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R QC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z","10244":"VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB"},G:{"1":"ND OD PD QD RD SD TD UD VD WC XC JC WD KC YC ZC aC bC cC XD LC dC eC fC gC hC YD MC iC jC kC lC mC","2":"UC CD oC","2820":"E DD ED FD GD HD ID JD KD LD MD"},H:{"2":"ZD"},I:{"2":"NC J aD bD cD dD oC","260":"I","2308":"eD fD"},J:{"2":"D","2308":"A"},K:{"2":"A B C HC nC","16":"IC","8196":"H"},L:{"8196":"I"},M:{"1028":"GC"},N:{"2":"A B"},O:{"8196":"JC"},P:{"2052":"gD hD","2308":"J","8196":"1 2 3 4 5 6 7 8 iD jD kD VC lD mD nD oD pD KC LC MC qD"},Q:{"8196":"rD"},R:{"8196":"sD"},S:{"4100":"tD uD"}},B:5,C:"Synchronous Clipboard API",D:true};
