{"name": "kavya-amr-frontend", "version": "1.0.0", "description": "KAVYA AMR System - Frontend Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "cross-env ANALYZE=true next build", "export": "next export"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@react-three/drei": "^9.92.7", "@react-three/fiber": "^8.15.12", "@react-three/postprocessing": "^2.15.11", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.14.2", "@tanstack/react-query-devtools": "^5.14.2", "@types/crypto-js": "^4.2.1", "@types/d3": "^7.4.3", "@types/node": "^20.17.57", "@types/plotly.js": "^2.12.29", "@types/react": "^18.3.23", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^18.2.18", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "@types/three": "^0.159.0", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "crypto-js": "^4.2.0", "d3": "^7.8.5", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "i18next": "^23.7.6", "immer": "^10.0.3", "jose": "^5.1.3", "jspdf": "^2.5.1", "lottie-react": "^2.4.0", "next": "^14.2.29", "next-auth": "^4.24.5", "next-i18next": "^15.2.0", "next-pwa": "^5.6.0", "next-seo": "^6.4.0", "plotly.js": "^2.27.1", "postcss": "^8.4.32", "react": "^18.3.1", "react-datepicker": "^4.25.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-image-crop": "^10.1.8", "react-loading-skeleton": "^3.3.1", "react-pdf": "^7.6.0", "react-plotly.js": "^2.6.0", "react-query": "^3.39.3", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "swr": "^2.2.4", "tailwindcss": "^3.4.17", "three": "^0.159.0", "typescript": "^5.8.3", "workbox-webpack-plugin": "^7.0.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.11", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "chromatic": "^10.2.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "msw": "^2.0.11", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1", "^@/components/(.*)$": "<rootDir>/src/components/$1", "^@/pages/(.*)$": "<rootDir>/src/pages/$1", "^@/hooks/(.*)$": "<rootDir>/src/hooks/$1", "^@/utils/(.*)$": "<rootDir>/src/utils/$1", "^@/types/(.*)$": "<rootDir>/src/types/$1", "^@/styles/(.*)$": "<rootDir>/src/styles/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/**/*.stories.{js,jsx,ts,tsx}", "!src/pages/_app.tsx", "!src/pages/_document.tsx"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "pwa": {"dest": "public", "register": true, "skipWaiting": true, "runtimeCaching": [{"urlPattern": "/api/.*", "handler": "NetworkFirst", "options": {"cacheName": "api-cache", "expiration": {"maxEntries": 100, "maxAgeSeconds": 300}}}]}, "keywords": ["antimicrobial-resistance", "amr", "ai", "machine-learning", "healthcare", "nanobots", "quantum-computing", "genomics", "pathogen-detection", "drug-resistance", "precision-medicine", "global-health", "surveillance", "outbreak-detection", "biomarkers", "next.js", "react", "typescript", "tailwindcss", "three.js"], "author": {"name": "KAVYA Development Team", "email": "<EMAIL>", "url": "https://kavya-amr.org"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/kavya-amr/kavya-system.git"}, "bugs": {"url": "https://github.com/kavya-amr/kavya-system/issues"}, "homepage": "https://kavya-amr.org"}