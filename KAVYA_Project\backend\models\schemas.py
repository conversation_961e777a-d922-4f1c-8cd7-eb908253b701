"""
KAVYA AMR System - Pydantic Schemas
Data validation and serialization schemas
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum
import uuid


class SampleType(str, Enum):
    BLOOD = "blood"
    URINE = "urine"
    SWAB = "swab"
    TISSUE = "tissue"
    SPUTUM = "sputum"
    CSF = "csf"
    OTHER = "other"


class TherapyType(str, Enum):
    ANTIBIOTIC = "antibiotic"
    NANOBOT = "nanobot"
    COMBINATION = "combination"
    SUPPORTIVE = "supportive"


class DeploymentStatus(str, Enum):
    PLANNED = "planned"
    INITIATED = "initiated"
    IN_TRANSIT = "in_transit"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    FAILED = "failed"


# Base Schemas
class BaseResponse(BaseModel):
    """Base response schema with common fields"""
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    status: str = "success"
    message: Optional[str] = None


# Diagnostic Schemas
class DiagnosticRequest(BaseModel):
    """Request schema for diagnostic analysis"""
    patient_id: str = Field(..., description="Unique patient identifier")
    sample_type: SampleType = Field(..., description="Type of biological sample")
    sample_data: Optional[str] = Field(None, description="Base64 encoded sample data")
    image_data: Optional[str] = Field(None, description="Base64 encoded microscopy image")
    clinical_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Clinical metadata")
    priority: str = Field(default="normal", description="Analysis priority level")
    
    @validator('sample_data', 'image_data')
    def validate_data_present(cls, v, values):
        if not v and not values.get('sample_data') and not values.get('image_data'):
            raise ValueError('Either sample_data or image_data must be provided')
        return v


class PathogenIdentification(BaseModel):
    """Pathogen identification results"""
    primary_pathogen: str
    confidence: float = Field(..., ge=0, le=1)
    alternative_pathogens: List[Dict[str, Union[str, float]]] = Field(default_factory=list)
    gram_stain: Optional[str] = None
    morphology: Optional[str] = None
    biofilm_present: Optional[bool] = None


class ResistanceProfile(BaseModel):
    """Antimicrobial resistance profile"""
    resistant_antibiotics: List[str] = Field(default_factory=list)
    susceptible_antibiotics: List[str] = Field(default_factory=list)
    intermediate_antibiotics: List[str] = Field(default_factory=list)
    resistance_genes: List[str] = Field(default_factory=list)
    resistance_mechanisms: List[str] = Field(default_factory=list)
    novel_resistance_detected: bool = False


class TreatmentRecommendation(BaseModel):
    """Treatment recommendation"""
    primary_therapy: str
    alternative_therapies: List[str] = Field(default_factory=list)
    dosage_regimen: Optional[Dict[str, Any]] = None
    duration_days: Optional[int] = None
    monitoring_parameters: List[str] = Field(default_factory=list)
    contraindications: List[str] = Field(default_factory=list)
    nanobot_therapy_recommended: bool = False


class DiagnosticResponse(BaseResponse):
    """Response schema for diagnostic analysis"""
    analysis_id: str = Field(..., description="Unique analysis identifier")
    pathogen_identification: PathogenIdentification
    resistance_profile: ResistanceProfile
    biomarker_analysis: Optional[Dict[str, Any]] = None
    treatment_recommendations: TreatmentRecommendation
    confidence_score: float = Field(..., ge=0, le=1)
    processing_time: float = Field(..., description="Processing time in seconds")
    uncertainty_metrics: Optional[Dict[str, float]] = None


# Nanobot Schemas
class NanobotDeploymentRequest(BaseModel):
    """Request schema for nanobot deployment"""
    patient_id: str = Field(..., description="Unique patient identifier")
    target_pathogen: str = Field(..., description="Target pathogen species")
    target_site: Dict[str, float] = Field(..., description="Target anatomical coordinates")
    therapy_type: TherapyType = Field(default=TherapyType.NANOBOT)
    nanobot_count: int = Field(default=1000000, ge=1000, le=10000000)
    payload_type: str = Field(default="antimicrobial_peptides")
    payload_concentration: float = Field(default=100.0, ge=1.0, le=1000.0)
    delivery_method: str = Field(default="intravenous")
    
    @validator('target_site')
    def validate_coordinates(cls, v):
        required_keys = ['x', 'y', 'z']
        if not all(key in v for key in required_keys):
            raise ValueError('Target site must include x, y, z coordinates')
        return v


class NanobotDeploymentResponse(BaseResponse):
    """Response schema for nanobot deployment"""
    deployment_id: str = Field(..., description="Unique deployment identifier")
    status: DeploymentStatus
    estimated_delivery_time: float = Field(..., description="Estimated delivery time in minutes")
    nanobot_count: int
    target_coordinates: Dict[str, float]
    navigation_plan: Optional[Dict[str, Any]] = None
    monitoring_url: str = Field(..., description="URL for real-time monitoring")
    safety_parameters: Optional[Dict[str, Any]] = None


# Surveillance Schemas
class SurveillanceData(BaseModel):
    """Surveillance data submission schema"""
    institution: str = Field(..., description="Reporting institution")
    country: str = Field(..., description="Country code")
    region: Optional[str] = None
    reporting_period: str = Field(..., description="Reporting period (e.g., 2024-Q1)")
    pathogen_data: List[Dict[str, Any]] = Field(..., description="Pathogen surveillance data")
    resistance_data: List[Dict[str, Any]] = Field(..., description="Resistance surveillance data")
    outbreak_alerts: List[Dict[str, Any]] = Field(default_factory=list)
    data_quality_score: float = Field(default=1.0, ge=0, le=1)
    anonymization_applied: bool = Field(default=True)


class SurveillanceResponse(BaseResponse):
    """Response schema for surveillance data submission"""
    surveillance_id: str = Field(..., description="Unique surveillance record identifier")
    outbreak_risk_score: float = Field(..., ge=0, le=1, description="Calculated outbreak risk")
    trend_analysis: Dict[str, Any] = Field(..., description="Trend analysis results")
    recommendations: List[str] = Field(default_factory=list)
    global_context: Dict[str, Any] = Field(..., description="Global surveillance context")
    data_sharing_status: str = Field(default="shared")


# Patient Schemas
class PatientCreate(BaseModel):
    """Schema for creating patient records"""
    patient_id: str = Field(..., description="Unique patient identifier")
    age: Optional[int] = Field(None, ge=0, le=150)
    gender: Optional[str] = None
    medical_history: Optional[Dict[str, Any]] = None
    allergies: List[str] = Field(default_factory=list)
    current_medications: List[str] = Field(default_factory=list)
    consent_given: bool = Field(default=False)
    data_retention_days: int = Field(default=2555, description="Data retention period in days")


class PatientResponse(BaseResponse):
    """Response schema for patient operations"""
    patient_id: str
    created_at: datetime
    consent_status: bool
    anonymization_level: str = Field(default="standard")
    data_retention_until: Optional[datetime] = None


# Analysis Schemas
class AnalysisResponse(BaseModel):
    """Schema for analysis history"""
    analysis_id: str
    analysis_type: str
    created_at: datetime
    pathogen_identified: Optional[str] = None
    resistance_detected: bool = False
    confidence_score: float
    validation_status: str = Field(default="pending")


class TreatmentResponse(BaseModel):
    """Schema for treatment records"""
    treatment_id: str
    therapy_type: str
    primary_therapy: str
    start_date: Optional[datetime] = None
    treatment_response: Optional[str] = None
    nanobot_deployment_id: Optional[str] = None


# Error Schemas
class ErrorResponse(BaseModel):
    """Error response schema"""
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None


class ValidationError(BaseModel):
    """Validation error schema"""
    field: str
    message: str
    invalid_value: Optional[Any] = None


# Health Check Schemas
class HealthCheckResponse(BaseModel):
    """Health check response schema"""
    status: str = Field(..., description="Service health status")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = Field(default="1.0.0")
    dependencies: Dict[str, str] = Field(default_factory=dict)
    metrics: Optional[Dict[str, Any]] = None


# Batch Processing Schemas
class BatchDiagnosticRequest(BaseModel):
    """Schema for batch diagnostic requests"""
    requests: List[DiagnosticRequest] = Field(..., max_items=100)
    batch_id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    priority: str = Field(default="normal")


class BatchDiagnosticResponse(BaseResponse):
    """Response schema for batch diagnostic processing"""
    batch_id: str
    total_requests: int
    completed: int
    failed: int
    results: List[DiagnosticResponse] = Field(default_factory=list)
    errors: List[ErrorResponse] = Field(default_factory=list)


# Real-time Monitoring Schemas
class RealTimeUpdate(BaseModel):
    """Schema for real-time updates"""
    update_type: str = Field(..., description="Type of update")
    entity_id: str = Field(..., description="Entity being updated")
    data: Dict[str, Any] = Field(..., description="Update data")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class MonitoringMetrics(BaseModel):
    """Schema for monitoring metrics"""
    cpu_usage: float = Field(..., ge=0, le=100)
    memory_usage: float = Field(..., ge=0, le=100)
    gpu_usage: Optional[float] = Field(None, ge=0, le=100)
    active_analyses: int = Field(..., ge=0)
    queue_length: int = Field(..., ge=0)
    response_time_ms: float = Field(..., ge=0)
    error_rate: float = Field(..., ge=0, le=1)


# Configuration Schemas
class ModelConfig(BaseModel):
    """Schema for AI model configuration"""
    model_name: str
    model_version: str
    confidence_threshold: float = Field(default=0.8, ge=0, le=1)
    batch_size: int = Field(default=32, ge=1, le=256)
    max_sequence_length: int = Field(default=512, ge=1, le=2048)
    device: str = Field(default="cuda")
    enable_uncertainty: bool = Field(default=True)


class SystemConfig(BaseModel):
    """Schema for system configuration"""
    max_concurrent_requests: int = Field(default=100, ge=1, le=1000)
    request_timeout_seconds: int = Field(default=300, ge=30, le=3600)
    enable_monitoring: bool = Field(default=True)
    log_level: str = Field(default="INFO")
    enable_audit_logging: bool = Field(default=True)
    data_retention_days: int = Field(default=2555, ge=1, le=3650)
