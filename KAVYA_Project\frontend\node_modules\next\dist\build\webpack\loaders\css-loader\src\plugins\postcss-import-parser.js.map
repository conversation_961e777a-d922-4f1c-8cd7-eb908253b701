{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-import-parser.ts"], "names": ["parseNode", "atRule", "key", "parent", "type", "raws", "after<PERSON>ame", "trim", "length", "lastCommentIndex", "lastIndexOf", "matched", "slice", "match", "WEBPACK_IGNORE_COMMENT_REGEXP", "prevNode", "prev", "text", "nodes", "error", "Error", "node", "paramsNodes", "valueParser", "toString", "isStringValue", "url", "value", "toLowerCase", "stringify", "normalizeUrl", "isRequestable", "isUrlRequestable", "prefix", "queryParts", "split", "pop", "join", "mediaNodes", "media", "plugin", "options", "postcssPlugin", "prepare", "result", "parsedAtRules", "AtRule", "import", "parsedAtRule", "warn", "message", "push", "OnceExit", "resolvedAtRules", "Promise", "all", "map", "filter", "<PERSON><PERSON><PERSON>", "request", "requestify", "rootContext", "resolver", "context", "resolvedUrl", "resolveRequests", "Set", "resourcePath", "remove", "urlToNameMap", "Map", "index", "resolvedAtRule", "api", "newUrl", "importName", "get", "size", "set", "imports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postcss"], "mappings": ";;;;+BAsPA;;;eAAA;;;2EAtPwB;uBASjB;;;;;;AAEP,SAASA,UAAUC,MAAW,EAAEC,GAAQ;IACtC,iCAAiC;IACjC,IAAID,OAAOE,MAAM,CAACC,IAAI,KAAK,QAAQ;QACjC;IACF;IAEA,IACEH,OAAOI,IAAI,IACXJ,OAAOI,IAAI,CAACC,SAAS,IACrBL,OAAOI,IAAI,CAACC,SAAS,CAACC,IAAI,GAAGC,MAAM,GAAG,GACtC;QACA,MAAMC,mBAAmBR,OAAOI,IAAI,CAACC,SAAS,CAACI,WAAW,CAAC;QAC3D,MAAMC,UAAUV,OAAOI,IAAI,CAACC,SAAS,CAClCM,KAAK,CAACH,kBACNI,KAAK,CAACC,oCAA6B;QAEtC,IAAIH,WAAWA,OAAO,CAAC,EAAE,KAAK,QAAQ;YACpC;QACF;IACF;IAEA,MAAMI,WAAWd,OAAOe,IAAI;IAE5B,IAAID,YAAYA,SAASX,IAAI,KAAK,WAAW;QAC3C,MAAMO,UAAUI,SAASE,IAAI,CAACJ,KAAK,CAACC,oCAA6B;QAEjE,IAAIH,WAAWA,OAAO,CAAC,EAAE,KAAK,QAAQ;YACpC;QACF;IACF;IAEA,0DAA0D;IAC1D,IAAIV,OAAOiB,KAAK,EAAE;QAChB,MAAMC,QAAa,IAAIC,MACrB;QAGFD,MAAME,IAAI,GAAGpB;QAEb,MAAMkB;IACR;IAEA,MAAM,EAAED,OAAOI,WAAW,EAAE,GAAGC,IAAAA,2BAAW,EAACtB,MAAM,CAACC,IAAI;IAEtD,yBAAyB;IACzB,oCAAoC;IACpC,IACEoB,YAAYd,MAAM,KAAK,KACtBc,WAAW,CAAC,EAAE,CAAClB,IAAI,KAAK,YAAYkB,WAAW,CAAC,EAAE,CAAClB,IAAI,KAAK,YAC7D;QACA,MAAMe,QAAa,IAAIC,MAAM,CAAC,uBAAuB,EAAEnB,OAAOuB,QAAQ,GAAG,CAAC,CAAC;QAE3EL,MAAME,IAAI,GAAGpB;QAEb,MAAMkB;IACR;IAEA,IAAIM;IACJ,IAAIC;IAEJ,IAAIJ,WAAW,CAAC,EAAE,CAAClB,IAAI,KAAK,UAAU;QACpCqB,gBAAgB;QAChBC,MAAMJ,WAAW,CAAC,EAAE,CAACK,KAAK;IAC5B,OAAO;QACL,gDAAgD;QAChD,IAAIL,WAAW,CAAC,EAAE,CAACK,KAAK,CAACC,WAAW,OAAO,OAAO;YAChD,MAAMT,QAAa,IAAIC,MACrB,CAAC,uBAAuB,EAAEnB,OAAOuB,QAAQ,GAAG,CAAC,CAAC;YAGhDL,MAAME,IAAI,GAAGpB;YAEb,MAAMkB;QACR;QAEAM,gBACEH,WAAW,CAAC,EAAE,CAACJ,KAAK,CAACV,MAAM,KAAK,KAChCc,WAAW,CAAC,EAAE,CAACJ,KAAK,CAAC,EAAE,CAACd,IAAI,KAAK;QACnCsB,MAAMD,gBACFH,WAAW,CAAC,EAAE,CAACJ,KAAK,CAAC,EAAE,CAACS,KAAK,GAC7BJ,2BAAW,CAACM,SAAS,CAACP,WAAW,CAAC,EAAE,CAACJ,KAAK;IAChD;IAEAQ,MAAMI,IAAAA,mBAAY,EAACJ,KAAKD;IAExB,MAAMM,gBAAgBC,IAAAA,uBAAgB,EAACN;IACvC,IAAIO;IAEJ,IAAIF,eAAe;QACjB,MAAMG,aAAaR,IAAIS,KAAK,CAAC;QAE7B,IAAID,WAAW1B,MAAM,GAAG,GAAG;YACzBkB,MAAMQ,WAAWE,GAAG;YACpBH,SAASC,WAAWG,IAAI,CAAC;QAC3B;IACF;IAEA,gDAAgD;IAChD,IAAIX,IAAInB,IAAI,GAAGC,MAAM,KAAK,GAAG;QAC3B,MAAMW,QAAa,IAAIC,MAAM,CAAC,uBAAuB,EAAEnB,OAAOuB,QAAQ,GAAG,CAAC,CAAC;QAE3EL,MAAME,IAAI,GAAGpB;QAEb,MAAMkB;IACR;IAEA,MAAMmB,aAAahB,YAAYV,KAAK,CAAC;IACrC,IAAI2B;IAEJ,IAAID,WAAW9B,MAAM,GAAG,GAAG;QACzB+B,QAAQhB,2BAAW,CAACM,SAAS,CAACS,YAAY/B,IAAI,GAAGqB,WAAW;IAC9D;IAEA,6CAA6C;IAC7C,OAAO;QAAE3B;QAAQgC;QAAQP;QAAKa;QAAOR;IAAc;AACrD;AAEA,MAAMS,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACfC,SAAQC,MAAW;YACjB,MAAMC,gBAAuB,EAAE;YAE/B,OAAO;gBACLC,QAAQ;oBACNC,QAAO9C,MAAW;wBAChB,IAAI+C;wBAEJ,IAAI;4BACF,qDAAqD;4BACrDA,eAAehD,UAAUC,QAAQ,UAAU2C;wBAC7C,EAAE,OAAOzB,OAAY;4BACnByB,OAAOK,IAAI,CAAC9B,MAAM+B,OAAO,EAAE;gCAAE7B,MAAMF,MAAME,IAAI;4BAAC;wBAChD;wBAEA,IAAI,CAAC2B,cAAc;4BACjB;wBACF;wBAEAH,cAAcM,IAAI,CAACH;oBACrB;gBACF;gBACA,MAAMI;oBACJ,IAAIP,cAAcrC,MAAM,KAAK,GAAG;wBAC9B;oBACF;oBAEA,MAAM6C,kBAAkB,MAAMC,QAAQC,GAAG,CACvCV,cAAcW,GAAG,CAAC,OAAOR;wBACvB,MAAM,EAAE/C,MAAM,EAAE8B,aAAa,EAAEE,MAAM,EAAEP,GAAG,EAAEa,KAAK,EAAE,GAAGS;wBAEtD,IAAIP,QAAQgB,MAAM,EAAE;4BAClB,MAAMC,WAAW,MAAMjB,QAAQgB,MAAM,CAAC/B,KAAKa;4BAE3C,IAAI,CAACmB,UAAU;gCACb;4BACF;wBACF;wBAEA,IAAI3B,eAAe;4BACjB,MAAM4B,UAAUC,IAAAA,iBAAU,EAAClC,KAAKe,QAAQoB,WAAW;4BAEnD,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE,GAAGtB;4BAC9B,MAAMuB,cAAc,MAAMC,IAAAA,sBAAe,EAACH,UAAUC,SAAS;mCACxD,IAAIG,IAAI;oCAACP;oCAASjC;iCAAI;6BAC1B;4BAED,IAAI,CAACsC,aAAa;gCAChB;4BACF;4BAEA,IAAIA,gBAAgBvB,QAAQ0B,YAAY,EAAE;gCACxClE,OAAOmE,MAAM;gCAEb;4BACF;4BAEAnE,OAAOmE,MAAM;4BAEb,6CAA6C;4BAC7C,OAAO;gCAAE1C,KAAKsC;gCAAazB;gCAAON;gCAAQF;4BAAc;wBAC1D;wBAEA9B,OAAOmE,MAAM;wBAEb,6CAA6C;wBAC7C,OAAO;4BAAE1C;4BAAKa;4BAAON;4BAAQF;wBAAc;oBAC7C;oBAGF,MAAMsC,eAAe,IAAIC;oBAEzB,IAAK,IAAIC,QAAQ,GAAGA,SAASlB,gBAAgB7C,MAAM,GAAG,GAAG+D,QAAS;wBAChE,MAAMC,iBAAiBnB,eAAe,CAACkB,MAAM;wBAE7C,IAAI,CAACC,gBAAgB;4BAEnB;wBACF;wBAEA,MAAM,EAAE9C,GAAG,EAAEK,aAAa,EAAEQ,KAAK,EAAE,GAAGiC;wBAEtC,IAAI,CAACzC,eAAe;4BAClBU,QAAQgC,GAAG,CAACtB,IAAI,CAAC;gCAAEzB;gCAAKa;gCAAOgC;4BAAM;4BAGrC;wBACF;wBAEA,MAAM,EAAEtC,MAAM,EAAE,GAAGuC;wBACnB,MAAME,SAASzC,SAAS,CAAC,EAAEA,OAAO,CAAC,EAAEP,IAAI,CAAC,GAAGA;wBAC7C,IAAIiD,aAAaN,aAAaO,GAAG,CAACF;wBAElC,IAAI,CAACC,YAAY;4BACfA,aAAa,CAAC,6BAA6B,EAAEN,aAAaQ,IAAI,CAAC,GAAG,CAAC;4BACnER,aAAaS,GAAG,CAACJ,QAAQC;4BAEzBlC,QAAQsC,OAAO,CAAC5B,IAAI,CAAC;gCACnB/C,MAAM;gCACNuE;gCACAjD,KAAKe,QAAQuC,UAAU,CAACN;gCACxBH;4BACF;wBACF;wBAEA9B,QAAQgC,GAAG,CAACtB,IAAI,CAAC;4BAAEwB;4BAAYpC;4BAAOgC;wBAAM;oBAC9C;gBACF;YACF;QACF;IACF;AACF;AAEA/B,OAAOyC,OAAO,GAAG;MAEjB,WAAezC"}