"""
KAVYA AMR System - Core Configuration
Centralized configuration management with environment variable support
"""

import os
from typing import List, Optional, Union
from pydantic import BaseSettings, validator, Field
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # =============================================================================
    # Application Settings
    # =============================================================================
    app_name: str = Field(default="KAVYA AMR System", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    app_environment: str = Field(default="development", env="APP_ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # =============================================================================
    # API Configuration
    # =============================================================================
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_workers: int = Field(default=4, env="API_WORKERS")
    api_reload: bool = Field(default=True, env="API_RELOAD")
    api_prefix: str = Field(default="/api/v1", env="API_PREFIX")
    
    # =============================================================================
    # Database Configuration
    # =============================================================================
    # PostgreSQL
    postgres_host: str = Field(default="localhost", env="POSTGRES_HOST")
    postgres_port: int = Field(default=5432, env="POSTGRES_PORT")
    postgres_db: str = Field(default="kavya_amr", env="POSTGRES_DB")
    postgres_user: str = Field(default="kavya_user", env="POSTGRES_USER")
    postgres_password: str = Field(default="password", env="POSTGRES_PASSWORD")
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    
    # MongoDB
    mongodb_host: str = Field(default="localhost", env="MONGODB_HOST")
    mongodb_port: int = Field(default=27017, env="MONGODB_PORT")
    mongodb_db: str = Field(default="kavya_genomics", env="MONGODB_DB")
    mongodb_user: str = Field(default="kavya_mongo", env="MONGODB_USER")
    mongodb_password: str = Field(default="password", env="MONGODB_PASSWORD")
    mongodb_url: Optional[str] = Field(default=None, env="MONGODB_URL")
    
    # Redis
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=0, env="REDIS_DB")
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    
    # =============================================================================
    # Security & Authentication
    # =============================================================================
    secret_key: str = Field(default="your-secret-key-change-this", env="SECRET_KEY")
    jwt_secret_key: str = Field(default="your-jwt-secret", env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    jwt_expiration_hours: int = Field(default=24, env="JWT_EXPIRATION_HOURS")
    bcrypt_rounds: int = Field(default=12, env="BCRYPT_ROUNDS")
    
    # OAuth2
    oauth2_client_id: Optional[str] = Field(default=None, env="OAUTH2_CLIENT_ID")
    oauth2_client_secret: Optional[str] = Field(default=None, env="OAUTH2_CLIENT_SECRET")
    oauth2_redirect_uri: Optional[str] = Field(default=None, env="OAUTH2_REDIRECT_URI")
    
    # =============================================================================
    # AI/ML Model Configuration
    # =============================================================================
    pathogen_model_path: str = Field(default="./models/pathogen_classifier.pth", env="PATHOGEN_MODEL_PATH")
    genomic_model_path: str = Field(default="./models/genomic_transformer.pth", env="GENOMIC_MODEL_PATH")
    fusion_model_path: str = Field(default="./models/multimodal_fusion.pth", env="FUSION_MODEL_PATH")
    quantum_model_path: str = Field(default="./models/quantum_predictor.pkl", env="QUANTUM_MODEL_PATH")
    
    model_batch_size: int = Field(default=32, env="MODEL_BATCH_SIZE")
    model_max_sequence_length: int = Field(default=512, env="MODEL_MAX_SEQUENCE_LENGTH")
    model_confidence_threshold: float = Field(default=0.8, env="MODEL_CONFIDENCE_THRESHOLD")
    model_device: str = Field(default="cuda", env="MODEL_DEVICE")
    
    # =============================================================================
    # Cloud Storage & Services
    # =============================================================================
    # AWS
    aws_access_key_id: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    aws_secret_access_key: Optional[str] = Field(default=None, env="AWS_SECRET_ACCESS_KEY")
    aws_region: str = Field(default="us-east-1", env="AWS_REGION")
    aws_s3_bucket: Optional[str] = Field(default=None, env="AWS_S3_BUCKET")
    aws_s3_models_bucket: Optional[str] = Field(default=None, env="AWS_S3_MODELS_BUCKET")
    
    # GCP
    gcp_project_id: Optional[str] = Field(default=None, env="GCP_PROJECT_ID")
    gcp_service_account_key_path: Optional[str] = Field(default=None, env="GCP_SERVICE_ACCOUNT_KEY_PATH")
    gcp_storage_bucket: Optional[str] = Field(default=None, env="GCP_STORAGE_BUCKET")
    
    # =============================================================================
    # Quantum Computing
    # =============================================================================
    ibm_quantum_token: Optional[str] = Field(default=None, env="IBM_QUANTUM_TOKEN")
    ibm_quantum_backend: str = Field(default="ibmq_qasm_simulator", env="IBM_QUANTUM_BACKEND")
    pennylane_device: str = Field(default="default.qubit", env="PENNYLANE_DEVICE")
    pennylane_shots: int = Field(default=1000, env="PENNYLANE_SHOTS")
    
    # =============================================================================
    # Blockchain & Distributed Systems
    # =============================================================================
    blockchain_network: str = Field(default="polygon-mumbai", env="BLOCKCHAIN_NETWORK")
    blockchain_rpc_url: Optional[str] = Field(default=None, env="BLOCKCHAIN_RPC_URL")
    blockchain_private_key: Optional[str] = Field(default=None, env="BLOCKCHAIN_PRIVATE_KEY")
    blockchain_contract_address: Optional[str] = Field(default=None, env="BLOCKCHAIN_CONTRACT_ADDRESS")
    
    ipfs_node_url: str = Field(default="http://localhost:5001", env="IPFS_NODE_URL")
    ipfs_gateway_url: str = Field(default="http://localhost:8080", env="IPFS_GATEWAY_URL")
    
    # =============================================================================
    # Monitoring & Observability
    # =============================================================================
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    prometheus_metrics_path: str = Field(default="/metrics", env="PROMETHEUS_METRICS_PATH")
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    sentry_environment: str = Field(default="development", env="SENTRY_ENVIRONMENT")
    
    # =============================================================================
    # External APIs & Services
    # =============================================================================
    ncbi_api_key: Optional[str] = Field(default=None, env="NCBI_API_KEY")
    ncbi_email: Optional[str] = Field(default=None, env="NCBI_EMAIL")
    kaggle_username: Optional[str] = Field(default=None, env="KAGGLE_USERNAME")
    kaggle_key: Optional[str] = Field(default=None, env="KAGGLE_KEY")
    who_glass_api_key: Optional[str] = Field(default=None, env="WHO_GLASS_API_KEY")
    
    # =============================================================================
    # Nanobot Simulation
    # =============================================================================
    nanobot_simulation_engine: str = Field(default="unity3d", env="NANOBOT_SIMULATION_ENGINE")
    nanobot_physics_timestep: float = Field(default=0.001, env="NANOBOT_PHYSICS_TIMESTEP")
    nanobot_max_particles: int = Field(default=10000, env="NANOBOT_MAX_PARTICLES")
    nanobot_visualization_port: int = Field(default=8080, env="NANOBOT_VISUALIZATION_PORT")
    
    # =============================================================================
    # Notification Services
    # =============================================================================
    # Email
    smtp_host: str = Field(default="smtp.gmail.com", env="SMTP_HOST")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    smtp_username: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    smtp_password: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    smtp_use_tls: bool = Field(default=True, env="SMTP_USE_TLS")
    
    # Slack
    slack_bot_token: Optional[str] = Field(default=None, env="SLACK_BOT_TOKEN")
    slack_channel: str = Field(default="#kavya-alerts", env="SLACK_CHANNEL")
    
    # =============================================================================
    # Development & Testing
    # =============================================================================
    test_database_url: Optional[str] = Field(default=None, env="TEST_DATABASE_URL")
    enable_cors: bool = Field(default=True, env="ENABLE_CORS")
    cors_origins: List[str] = Field(default=["http://localhost:3000"], env="CORS_ORIGINS")
    enable_docs: bool = Field(default=True, env="ENABLE_DOCS")
    enable_redoc: bool = Field(default=True, env="ENABLE_REDOC")
    
    max_upload_size: str = Field(default="100MB", env="MAX_UPLOAD_SIZE")
    request_timeout: int = Field(default=300, env="REQUEST_TIMEOUT")
    connection_pool_size: int = Field(default=20, env="CONNECTION_POOL_SIZE")
    
    # =============================================================================
    # Compliance & Privacy
    # =============================================================================
    hipaa_encryption_enabled: bool = Field(default=True, env="HIPAA_ENCRYPTION_ENABLED")
    hipaa_audit_logging: bool = Field(default=True, env="HIPAA_AUDIT_LOGGING")
    hipaa_data_retention_days: int = Field(default=2555, env="HIPAA_DATA_RETENTION_DAYS")
    
    gdpr_enabled: bool = Field(default=True, env="GDPR_ENABLED")
    gdpr_data_retention_days: int = Field(default=1095, env="GDPR_DATA_RETENTION_DAYS")
    gdpr_consent_required: bool = Field(default=True, env="GDPR_CONSENT_REQUIRED")
    
    anonymization_enabled: bool = Field(default=True, env="ANONYMIZATION_ENABLED")
    differential_privacy_epsilon: float = Field(default=1.0, env="DIFFERENTIAL_PRIVACY_EPSILON")
    
    # =============================================================================
    # Feature Flags
    # =============================================================================
    enable_quantum_simulation: bool = Field(default=True, env="ENABLE_QUANTUM_SIMULATION")
    enable_nanobot_control: bool = Field(default=True, env="ENABLE_NANOBOT_CONTROL")
    enable_blockchain_logging: bool = Field(default=True, env="ENABLE_BLOCKCHAIN_LOGGING")
    enable_federated_learning: bool = Field(default=True, env="ENABLE_FEDERATED_LEARNING")
    enable_real_time_monitoring: bool = Field(default=True, env="ENABLE_REAL_TIME_MONITORING")
    enable_global_surveillance: bool = Field(default=True, env="ENABLE_GLOBAL_SURVEILLANCE")
    
    # =============================================================================
    # Rate Limiting
    # =============================================================================
    rate_limit_requests_per_minute: int = Field(default=100, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    rate_limit_burst_size: int = Field(default=20, env="RATE_LIMIT_BURST_SIZE")
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    
    # =============================================================================
    # Validators
    # =============================================================================
    @validator("database_url", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return f"postgresql://{values.get('postgres_user')}:{values.get('postgres_password')}@{values.get('postgres_host')}:{values.get('postgres_port')}/{values.get('postgres_db')}"
    
    @validator("mongodb_url", pre=True)
    def assemble_mongodb_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return f"mongodb://{values.get('mongodb_user')}:{values.get('mongodb_password')}@{values.get('mongodb_host')}:{values.get('mongodb_port')}/{values.get('mongodb_db')}"
    
    @validator("redis_url", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        password_part = f":{values.get('redis_password')}@" if values.get('redis_password') else ""
        return f"redis://{password_part}{values.get('redis_host')}:{values.get('redis_port')}/{values.get('redis_db')}"
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Global settings instance
settings = get_settings()
