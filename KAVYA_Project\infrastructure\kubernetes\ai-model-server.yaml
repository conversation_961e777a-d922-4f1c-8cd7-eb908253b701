apiVersion: apps/v1
kind: Deployment
metadata:
  name: kavya-ai-models
  namespace: kavya-amr
  labels:
    app: kavya-ai-models
    component: ml-inference
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: kavya-ai-models
  template:
    metadata:
      labels:
        app: kavya-ai-models
        component: ml-inference
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8001"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: kavya-ai-models-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: kavya-ai-models
        image: kavya/ai-models:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8001
          name: http
          protocol: TCP
        env:
        - name: MODEL_PATH
          value: "/models"
        - name: DEVICE
          value: "cuda"
        - name: BATCH_SIZE
          value: "32"
        - name: MAX_SEQUENCE_LENGTH
          value: "512"
        - name: CONFIDENCE_THRESHOLD
          value: "0.8"
        - name: LOG_LEVEL
          value: "INFO"
        - name: PROMETHEUS_METRICS_PATH
          value: "/metrics"
        envFrom:
        - configMapRef:
            name: kavya-ai-config
        resources:
          requests:
            cpu: 2
            memory: 8Gi
            nvidia.com/gpu: 1
          limits:
            cpu: 4
            memory: 16Gi
            nvidia.com/gpu: 1
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: models-storage
          mountPath: /models
          readOnly: true
        - name: model-cache
          mountPath: /cache
        - name: gpu-metrics
          mountPath: /dev/nvidia0
          readOnly: true
      volumes:
      - name: models-storage
        persistentVolumeClaim:
          claimName: kavya-models-pvc
      - name: model-cache
        emptyDir:
          sizeLimit: 10Gi
      - name: gpu-metrics
        hostPath:
          path: /dev/nvidia0
      nodeSelector:
        accelerator: nvidia-tesla-v100
        kubernetes.io/arch: amd64
      tolerations:
      - key: "nvidia.com/gpu"
        operator: "Exists"
        effect: "NoSchedule"
      - key: "kavya.io/dedicated"
        operator: "Equal"
        value: "ai-models"
        effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: accelerator
                operator: In
                values:
                - nvidia-tesla-v100
                - nvidia-tesla-a100
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - kavya-ai-models
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-ai-models-service
  namespace: kavya-amr
  labels:
    app: kavya-ai-models
    component: ml-inference
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8001
    protocol: TCP
    name: http
  selector:
    app: kavya-ai-models
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kavya-ai-models-sa
  namespace: kavya-amr
  labels:
    app: kavya-ai-models
    component: ml-inference
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: kavya-amr
  name: kavya-ai-models-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kavya-ai-models-rolebinding
  namespace: kavya-amr
subjects:
- kind: ServiceAccount
  name: kavya-ai-models-sa
  namespace: kavya-amr
roleRef:
  kind: Role
  name: kavya-ai-models-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: kavya-ai-models-hpa
  namespace: kavya-amr
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: kavya-ai-models
  minReplicas: 2
  maxReplicas: 6
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: gpu_utilization
      target:
        type: AverageValue
        averageValue: "70"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600
      policies:
      - type: Percent
        value: 25
        periodSeconds: 120
    scaleUp:
      stabilizationWindowSeconds: 120
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: kavya-ai-models-pdb
  namespace: kavya-amr
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: kavya-ai-models
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kavya-ai-config
  namespace: kavya-amr
  labels:
    app: kavya-ai-models
    component: ml-inference
data:
  # Model Configuration
  PATHOGEN_MODEL_PATH: "/models/pathogen_classifier.pth"
  GENOMIC_MODEL_PATH: "/models/genomic_transformer.pth"
  FUSION_MODEL_PATH: "/models/multimodal_fusion.pth"
  QUANTUM_MODEL_PATH: "/models/quantum_predictor.pkl"
  
  # Inference Configuration
  MODEL_BATCH_SIZE: "32"
  MODEL_MAX_SEQUENCE_LENGTH: "512"
  MODEL_CONFIDENCE_THRESHOLD: "0.8"
  MODEL_DEVICE: "cuda"
  
  # Performance Configuration
  MAX_CONCURRENT_REQUESTS: "100"
  REQUEST_TIMEOUT: "300"
  MODEL_CACHE_SIZE: "10GB"
  
  # Monitoring Configuration
  ENABLE_METRICS: "true"
  METRICS_PORT: "8001"
  METRICS_PATH: "/metrics"
  
  # Logging Configuration
  LOG_LEVEL: "INFO"
  LOG_FORMAT: "json"
  
  # Feature Flags
  ENABLE_QUANTUM_SIMULATION: "true"
  ENABLE_ATTENTION_VISUALIZATION: "true"
  ENABLE_UNCERTAINTY_QUANTIFICATION: "true"
