apiVersion: apps/v1
kind: Deployment
metadata:
  name: kavya-frontend
  namespace: kavya-amr
  labels:
    app: kavya-frontend
    component: ui
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: kavya-frontend
  template:
    metadata:
      labels:
        app: kavya-frontend
        component: ui
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/api/metrics"
    spec:
      serviceAccountName: kavya-frontend-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: kavya-frontend
        image: kavya/frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: NEXT_PUBLIC_API_URL
          value: "http://kavya-backend-service"
        - name: NEXT_PUBLIC_WS_URL
          value: "ws://kavya-backend-service"
        - name: NEXT_PUBLIC_APP_VERSION
          value: "1.0.0"
        - name: NEXT_PUBLIC_ENVIRONMENT
          value: "production"
        - name: NODE_ENV
          value: "production"
        envFrom:
        - configMapRef:
            name: kavya-frontend-config
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
          readOnly: true
        - name: static-assets
          mountPath: /app/.next/static
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: nginx-config
        configMap:
          name: kavya-nginx-config
      - name: static-assets
        emptyDir: {}
      - name: logs
        emptyDir: {}
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "kavya.io/dedicated"
        operator: "Equal"
        value: "frontend"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - kavya-frontend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-frontend-service
  namespace: kavya-amr
  labels:
    app: kavya-frontend
    component: ui
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: kavya-frontend
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kavya-frontend-sa
  namespace: kavya-amr
  labels:
    app: kavya-frontend
    component: ui
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: kavya-amr
  name: kavya-frontend-role
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kavya-frontend-rolebinding
  namespace: kavya-amr
subjects:
- kind: ServiceAccount
  name: kavya-frontend-sa
  namespace: kavya-amr
roleRef:
  kind: Role
  name: kavya-frontend-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: kavya-frontend-hpa
  namespace: kavya-amr
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: kavya-frontend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: kavya-frontend-pdb
  namespace: kavya-amr
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: kavya-frontend
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kavya-frontend-config
  namespace: kavya-amr
  labels:
    app: kavya-frontend
    component: ui
data:
  # Application Configuration
  NEXT_PUBLIC_APP_NAME: "KAVYA AMR System"
  NEXT_PUBLIC_APP_DESCRIPTION: "AI-Powered Antimicrobial Resistance Prediction"
  NEXT_PUBLIC_APP_VERSION: "1.0.0"
  
  # API Configuration
  NEXT_PUBLIC_API_TIMEOUT: "30000"
  NEXT_PUBLIC_MAX_FILE_SIZE: "10485760"  # 10MB
  NEXT_PUBLIC_SUPPORTED_FORMATS: "jpg,jpeg,png,tiff,fastq,fasta"
  
  # Feature Flags
  NEXT_PUBLIC_ENABLE_QUANTUM_FEATURES: "true"
  NEXT_PUBLIC_ENABLE_NANOBOT_SIMULATION: "true"
  NEXT_PUBLIC_ENABLE_REAL_TIME_MONITORING: "true"
  NEXT_PUBLIC_ENABLE_ADVANCED_ANALYTICS: "true"
  
  # UI Configuration
  NEXT_PUBLIC_THEME: "medical"
  NEXT_PUBLIC_DEFAULT_LANGUAGE: "en"
  NEXT_PUBLIC_TIMEZONE: "UTC"
  
  # Monitoring Configuration
  NEXT_PUBLIC_ENABLE_ANALYTICS: "true"
  NEXT_PUBLIC_SENTRY_DSN: ""
  
  # Security Configuration
  NEXT_PUBLIC_CSP_ENABLED: "true"
  NEXT_PUBLIC_SECURE_HEADERS: "true"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kavya-nginx-config
  namespace: kavya-amr
  labels:
    app: kavya-frontend
    component: nginx
data:
  default.conf: |
    upstream nextjs_upstream {
        server 127.0.0.1:3000;
    }
    
    server {
        listen 80;
        server_name _;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:;" always;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied expired no-cache no-store private must-revalidate auth;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/javascript
            application/xml+rss
            application/json;
        
        # Static files caching
        location /_next/static/ {
            alias /app/.next/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # API routes
        location /api/ {
            proxy_pass http://nextjs_upstream;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
        
        # WebSocket support
        location /ws/ {
            proxy_pass http://nextjs_upstream;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # All other routes
        location / {
            proxy_pass http://nextjs_upstream;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
