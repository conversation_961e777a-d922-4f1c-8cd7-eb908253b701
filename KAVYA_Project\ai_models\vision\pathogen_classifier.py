"""
KAVYA AMR System - Pathogen Classification Model
Advanced computer vision model for bacterial pathogen identification
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from torchvision.models import efficientnet_b7, EfficientNet_B7_Weights
import numpy as np
from typing import Dict, List, Tuple, Optional
import cv2
from PIL import Image
import logging

logger = logging.getLogger(__name__)


class AttentionModule(nn.Module):
    """Spatial attention mechanism for interpretability"""
    
    def __init__(self, in_channels: int):
        super(AttentionModule, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, in_channels // 8, kernel_size=1)
        self.conv2 = nn.Conv2d(in_channels // 8, 1, kernel_size=1)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        attention = self.conv1(x)
        attention = F.relu(attention)
        attention = self.conv2(attention)
        attention = self.sigmoid(attention)
        return x * attention, attention


class PathogenClassifier(nn.Module):
    """
    Advanced pathogen classification model based on EfficientNet-B7
    with attention mechanisms for interpretability
    """
    
    def __init__(self, 
                 num_classes: int = 50,
                 dropout_rate: float = 0.3,
                 use_attention: bool = True):
        super(PathogenClassifier, self).__init__()
        
        self.num_classes = num_classes
        self.use_attention = use_attention
        
        # Load pre-trained EfficientNet-B7 backbone
        self.backbone = efficientnet_b7(weights=EfficientNet_B7_Weights.IMAGENET1K_V1)
        
        # Remove the final classifier
        self.features = nn.Sequential(*list(self.backbone.children())[:-1])
        
        # Get the number of features from the backbone
        backbone_features = self.backbone.classifier.in_features
        
        # Attention mechanism
        if self.use_attention:
            self.attention = AttentionModule(backbone_features)
        
        # Custom classifier head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout_rate),
            nn.Linear(backbone_features, 1024),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(1024),
            nn.Dropout(dropout_rate),
            nn.Linear(1024, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
            nn.Dropout(dropout_rate),
            nn.Linear(512, num_classes)
        )
        
        # Auxiliary outputs for multi-task learning
        self.gram_stain_classifier = nn.Linear(512, 3)  # positive, negative, variable
        self.morphology_classifier = nn.Linear(512, 10)  # different morphologies
        self.biofilm_detector = nn.Linear(512, 2)  # biofilm present/absent
        
        # Uncertainty estimation
        self.uncertainty_head = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # Extract features using backbone
        features = self.features(x)
        
        # Apply attention if enabled
        attention_map = None
        if self.use_attention:
            features, attention_map = self.attention(features)
        
        # Global average pooling
        pooled_features = F.adaptive_avg_pool2d(features, (1, 1))
        pooled_features = pooled_features.view(pooled_features.size(0), -1)
        
        # Get intermediate features for auxiliary tasks
        intermediate_features = self.classifier[:-1](pooled_features)
        
        # Main classification
        pathogen_logits = self.classifier[-1](intermediate_features)
        
        # Auxiliary classifications
        gram_stain_logits = self.gram_stain_classifier(intermediate_features)
        morphology_logits = self.morphology_classifier(intermediate_features)
        biofilm_logits = self.biofilm_detector(intermediate_features)
        
        # Uncertainty estimation
        uncertainty = self.uncertainty_head(intermediate_features)
        
        return {
            'pathogen_logits': pathogen_logits,
            'gram_stain_logits': gram_stain_logits,
            'morphology_logits': morphology_logits,
            'biofilm_logits': biofilm_logits,
            'uncertainty': uncertainty,
            'attention_map': attention_map,
            'features': intermediate_features
        }


class PathogenPreprocessor:
    """Image preprocessing pipeline for pathogen classification"""
    
    def __init__(self, image_size: int = 224):
        self.image_size = image_size
        
        # Training transforms with augmentation
        self.train_transform = transforms.Compose([
            transforms.Resize((image_size + 32, image_size + 32)),
            transforms.RandomCrop((image_size, image_size)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomVerticalFlip(p=0.5),
            transforms.RandomRotation(degrees=45),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Validation/inference transforms
        self.val_transform = transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
    def preprocess_image(self, image: np.ndarray, training: bool = False) -> torch.Tensor:
        """
        Preprocess a single image for model input
        
        Args:
            image: Input image as numpy array (H, W, C)
            training: Whether to apply training augmentations
            
        Returns:
            Preprocessed image tensor
        """
        # Convert to PIL Image
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        
        pil_image = Image.fromarray(image)
        
        # Apply transforms
        if training:
            tensor = self.train_transform(pil_image)
        else:
            tensor = self.val_transform(pil_image)
            
        return tensor.unsqueeze(0)  # Add batch dimension
    
    def enhance_microscopy_image(self, image: np.ndarray) -> np.ndarray:
        """
        Apply microscopy-specific image enhancements
        
        Args:
            image: Input microscopy image
            
        Returns:
            Enhanced image
        """
        # Convert to LAB color space for better contrast enhancement
        lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        # Merge channels and convert back to RGB
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2RGB)
        
        # Gaussian blur for noise reduction
        enhanced = cv2.GaussianBlur(enhanced, (3, 3), 0)
        
        return enhanced


class PathogenInference:
    """Inference pipeline for pathogen classification"""
    
    def __init__(self, 
                 model_path: str,
                 device: str = 'cuda',
                 confidence_threshold: float = 0.8):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.confidence_threshold = confidence_threshold
        self.preprocessor = PathogenPreprocessor()
        
        # Load model
        self.model = self._load_model(model_path)
        self.model.eval()
        
        # Pathogen class names (should be loaded from config)
        self.pathogen_classes = self._load_class_names()
        self.gram_stain_classes = ['Gram-positive', 'Gram-negative', 'Gram-variable']
        self.morphology_classes = [
            'Cocci', 'Bacilli', 'Spirilla', 'Vibrio', 'Filamentous',
            'Pleomorphic', 'Coccobacilli', 'Curved', 'Branched', 'Other'
        ]
        
    def _load_model(self, model_path: str) -> PathogenClassifier:
        """Load trained model from checkpoint"""
        try:
            model = PathogenClassifier(num_classes=50)
            checkpoint = torch.load(model_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device)
            logger.info(f"Model loaded successfully from {model_path}")
            return model
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def _load_class_names(self) -> List[str]:
        """Load pathogen class names"""
        # This should be loaded from a configuration file
        return [
            'Staphylococcus aureus', 'Streptococcus pyogenes', 'Escherichia coli',
            'Pseudomonas aeruginosa', 'Klebsiella pneumoniae', 'Enterococcus faecalis',
            'Acinetobacter baumannii', 'Clostridium difficile', 'Bacteroides fragilis',
            'Proteus mirabilis', 'Enterobacter cloacae', 'Serratia marcescens',
            # ... add all 50 pathogen classes
        ] + [f'Pathogen_{i}' for i in range(13, 51)]  # Placeholder for remaining classes
    
    def predict(self, image: np.ndarray) -> Dict:
        """
        Perform pathogen classification on input image
        
        Args:
            image: Input microscopy image
            
        Returns:
            Dictionary containing predictions and metadata
        """
        try:
            # Preprocess image
            enhanced_image = self.preprocessor.enhance_microscopy_image(image)
            input_tensor = self.preprocessor.preprocess_image(enhanced_image, training=False)
            input_tensor = input_tensor.to(self.device)
            
            # Model inference
            with torch.no_grad():
                outputs = self.model(input_tensor)
            
            # Process outputs
            pathogen_probs = F.softmax(outputs['pathogen_logits'], dim=1)
            gram_stain_probs = F.softmax(outputs['gram_stain_logits'], dim=1)
            morphology_probs = F.softmax(outputs['morphology_logits'], dim=1)
            biofilm_probs = F.softmax(outputs['biofilm_logits'], dim=1)
            
            # Get top predictions
            pathogen_top5 = torch.topk(pathogen_probs, 5, dim=1)
            
            # Extract uncertainty
            uncertainty = outputs['uncertainty'].item()
            
            # Prepare results
            results = {
                'pathogen_identification': {
                    'primary_pathogen': self.pathogen_classes[pathogen_top5.indices[0][0].item()],
                    'confidence': pathogen_top5.values[0][0].item(),
                    'top_5_predictions': [
                        {
                            'pathogen': self.pathogen_classes[idx.item()],
                            'confidence': prob.item()
                        }
                        for idx, prob in zip(pathogen_top5.indices[0], pathogen_top5.values[0])
                    ]
                },
                'gram_stain': {
                    'prediction': self.gram_stain_classes[torch.argmax(gram_stain_probs).item()],
                    'confidence': torch.max(gram_stain_probs).item()
                },
                'morphology': {
                    'prediction': self.morphology_classes[torch.argmax(morphology_probs).item()],
                    'confidence': torch.max(morphology_probs).item()
                },
                'biofilm_detection': {
                    'present': torch.argmax(biofilm_probs).item() == 1,
                    'confidence': torch.max(biofilm_probs).item()
                },
                'uncertainty_score': uncertainty,
                'high_confidence': pathogen_top5.values[0][0].item() > self.confidence_threshold,
                'attention_map': outputs['attention_map'].cpu().numpy() if outputs['attention_map'] is not None else None
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise
    
    def batch_predict(self, images: List[np.ndarray]) -> List[Dict]:
        """
        Perform batch prediction on multiple images
        
        Args:
            images: List of input images
            
        Returns:
            List of prediction results
        """
        results = []
        for image in images:
            result = self.predict(image)
            results.append(result)
        return results


# Model training utilities
class PathogenTrainer:
    """Training utilities for pathogen classification model"""
    
    def __init__(self, model: PathogenClassifier, device: str = 'cuda'):
        self.model = model
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
    def train_epoch(self, dataloader, optimizer, criterion, epoch: int):
        """Train model for one epoch"""
        self.model.train()
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        for batch_idx, (images, labels) in enumerate(dataloader):
            images, labels = images.to(self.device), labels.to(self.device)
            
            optimizer.zero_grad()
            
            # Forward pass
            outputs = self.model(images)
            
            # Calculate losses
            pathogen_loss = criterion(outputs['pathogen_logits'], labels['pathogen'])
            gram_loss = criterion(outputs['gram_stain_logits'], labels['gram_stain'])
            morphology_loss = criterion(outputs['morphology_logits'], labels['morphology'])
            biofilm_loss = criterion(outputs['biofilm_logits'], labels['biofilm'])
            
            # Combined loss with weights
            total_loss_batch = (pathogen_loss + 
                              0.3 * gram_loss + 
                              0.3 * morphology_loss + 
                              0.2 * biofilm_loss)
            
            # Backward pass
            total_loss_batch.backward()
            optimizer.step()
            
            # Statistics
            total_loss += total_loss_batch.item()
            predictions = torch.argmax(outputs['pathogen_logits'], dim=1)
            correct_predictions += (predictions == labels['pathogen']).sum().item()
            total_samples += labels['pathogen'].size(0)
            
            if batch_idx % 100 == 0:
                logger.info(f'Epoch {epoch}, Batch {batch_idx}, Loss: {total_loss_batch.item():.4f}')
        
        avg_loss = total_loss / len(dataloader)
        accuracy = correct_predictions / total_samples
        
        return avg_loss, accuracy
