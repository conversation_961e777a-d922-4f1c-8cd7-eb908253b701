apiVersion: v1
kind: Namespace
metadata:
  name: kavya-amr
  labels:
    name: kavya-amr
    app.kubernetes.io/name: kavya-amr-system
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: kavya-amr-system
    app.kubernetes.io/managed-by: kubectl
  annotations:
    description: "KAVYA AMR System - AI-Powered Antimicrobial Resistance Prediction"
    contact: "<EMAIL>"
    compliance.hipaa: "true"
    compliance.gdpr: "true"
    security.level: "high"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: kavya-amr-quota
  namespace: kavya-amr
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    requests.nvidia.com/gpu: "4"
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "50"
    configmaps: "50"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: kavya-amr-limits
  namespace: kavya-amr
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: kavya-amr-network-policy
  namespace: kavya-amr
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: kavya-amr
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: kavya-amr
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
