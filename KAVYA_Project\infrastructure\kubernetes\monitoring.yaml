apiVersion: apps/v1
kind: Deployment
metadata:
  name: kavya-prometheus
  namespace: kavya-amr
  labels:
    app: kavya-prometheus
    component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kavya-prometheus
  template:
    metadata:
      labels:
        app: kavya-prometheus
        component: monitoring
    spec:
      serviceAccountName: kavya-prometheus-sa
      securityContext:
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
          name: prometheus
        args:
        - --config.file=/etc/prometheus/prometheus.yml
        - --storage.tsdb.path=/prometheus
        - --web.console.libraries=/etc/prometheus/console_libraries
        - --web.console.templates=/etc/prometheus/consoles
        - --web.enable-lifecycle
        - --storage.tsdb.retention.time=30d
        - --storage.tsdb.retention.size=50GB
        resources:
          requests:
            cpu: 500m
            memory: 2Gi
          limits:
            cpu: 2
            memory: 8Gi
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
        - name: prometheus-storage
          mountPath: /prometheus
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: prometheus-config
        configMap:
          name: kavya-prometheus-config
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: kavya-prometheus-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-prometheus-service
  namespace: kavya-amr
  labels:
    app: kavya-prometheus
    component: monitoring
spec:
  type: ClusterIP
  ports:
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: prometheus
  selector:
    app: kavya-prometheus
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kavya-grafana
  namespace: kavya-amr
  labels:
    app: kavya-grafana
    component: visualization
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kavya-grafana
  template:
    metadata:
      labels:
        app: kavya-grafana
        component: visualization
    spec:
      securityContext:
        runAsUser: 472
        runAsGroup: 472
        fsGroup: 472
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
          name: grafana
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kavya-monitoring-secrets
              key: grafana-admin-password
        - name: GF_DATABASE_TYPE
          value: postgres
        - name: GF_DATABASE_HOST
          value: kavya-postgres-service:5432
        - name: GF_DATABASE_NAME
          value: grafana
        - name: GF_DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: kavya-monitoring-secrets
              key: grafana-db-user
        - name: GF_DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kavya-monitoring-secrets
              key: grafana-db-password
        - name: GF_INSTALL_PLUGINS
          value: "grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel"
        resources:
          requests:
            cpu: 100m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 2Gi
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-config
          mountPath: /etc/grafana/provisioning
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: kavya-grafana-pvc
      - name: grafana-config
        configMap:
          name: kavya-grafana-config
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-grafana-service
  namespace: kavya-amr
  labels:
    app: kavya-grafana
    component: visualization
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: grafana
  selector:
    app: kavya-grafana
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: kavya-node-exporter
  namespace: kavya-amr
  labels:
    app: kavya-node-exporter
    component: monitoring
spec:
  selector:
    matchLabels:
      app: kavya-node-exporter
  template:
    metadata:
      labels:
        app: kavya-node-exporter
        component: monitoring
    spec:
      hostNetwork: true
      hostPID: true
      containers:
      - name: node-exporter
        image: prom/node-exporter:latest
        ports:
        - containerPort: 9100
          name: metrics
        args:
        - --path.procfs=/host/proc
        - --path.sysfs=/host/sys
        - --path.rootfs=/host/root
        - --collector.filesystem.ignored-mount-points
        - ^/(sys|proc|dev|host|etc|rootfs/var/lib/docker/containers|rootfs/var/lib/docker/overlay2|rootfs/run/docker/netns|rootfs/var/lib/docker/aufs)($$|/)
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        volumeMounts:
        - name: proc
          mountPath: /host/proc
          readOnly: true
        - name: sys
          mountPath: /host/sys
          readOnly: true
        - name: root
          mountPath: /host/root
          readOnly: true
      volumes:
      - name: proc
        hostPath:
          path: /proc
      - name: sys
        hostPath:
          path: /sys
      - name: root
        hostPath:
          path: /
      tolerations:
      - effect: NoSchedule
        operator: Exists
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-node-exporter-service
  namespace: kavya-amr
  labels:
    app: kavya-node-exporter
    component: monitoring
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 9100
    targetPort: 9100
    protocol: TCP
    name: metrics
  selector:
    app: kavya-node-exporter
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kavya-prometheus-sa
  namespace: kavya-amr
  labels:
    app: kavya-prometheus
    component: monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kavya-prometheus-role
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kavya-prometheus-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kavya-prometheus-role
subjects:
- kind: ServiceAccount
  name: kavya-prometheus-sa
  namespace: kavya-amr
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kavya-prometheus-config
  namespace: kavya-amr
  labels:
    app: kavya-prometheus
    component: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
    - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          - kavya-alertmanager-service:9093
    
    scrape_configs:
    - job_name: 'prometheus'
      static_configs:
      - targets: ['localhost:9090']
    
    - job_name: 'kavya-backend'
      kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
          - kavya-amr
      relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: kavya-backend-service
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: http
    
    - job_name: 'kavya-ai-models'
      kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
          - kavya-amr
      relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: kavya-ai-models-service
    
    - job_name: 'node-exporter'
      kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
          - kavya-amr
      relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: kavya-node-exporter-service
    
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - kavya-amr
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
