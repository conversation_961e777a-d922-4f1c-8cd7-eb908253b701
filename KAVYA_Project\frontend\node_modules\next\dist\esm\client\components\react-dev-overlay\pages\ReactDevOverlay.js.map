{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/ReactDevOverlay.tsx"], "names": ["React", "Bus", "ShadowPort<PERSON>", "BuildError", "Errors", "Error<PERSON>ou<PERSON><PERSON>", "Base", "ComponentStyles", "CssReset", "useErrorOverlayReducer", "shouldPreventDisplay", "errorType", "preventType", "includes", "ReactDevOverlay", "children", "preventDisplay", "globalOverlay", "state", "dispatch", "useEffect", "on", "off", "onComponentError", "useCallback", "_error", "_componentStack", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "isMounted", "displayPrevented", "onError", "message", "versionInfo", "isAppDir", "initialDisplayState", "undefined"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAE9B,YAAYC,SAAS,QAAO;AAC5B,SAASC,YAAY,QAAQ,sCAAqC;AAClE,SAASC,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,MAAM,QAAQ,+BAA8B;AACrD,SAASC,aAAa,QAAQ,kBAAiB;AAC/C,SAASC,IAAI,QAAQ,0BAAyB;AAC9C,SAASC,eAAe,QAAQ,qCAAoC;AACpE,SAASC,QAAQ,QAAQ,8BAA6B;AACtD,SAASC,sBAAsB,QAAQ,YAAW;AAIlD,MAAMC,uBAAuB,CAC3BC,WACAC;IAEA,IAAI,CAACA,eAAe,CAACD,WAAW;QAC9B,OAAO;IACT;IACA,OAAOC,YAAYC,QAAQ,CAACF;AAC9B;AAQA,eAAe,SAASG,gBAAgB,KAIjB;IAJiB,IAAA,EACtCC,QAAQ,EACRC,cAAc,EACdC,aAAa,EACQ,GAJiB;IAKtC,MAAM,CAACC,OAAOC,SAAS,GAAGV;IAE1BT,MAAMoB,SAAS,CAAC;QACdnB,IAAIoB,EAAE,CAACF;QACP,OAAO;YACLlB,IAAIqB,GAAG,CAACH;QACV;IACF,GAAG;QAACA;KAAS;IAEb,MAAMI,mBAAmBvB,MAAMwB,WAAW,CACxC,CAACC,QAAeC;IACd,yBAAyB;IAC3B,GACA,EAAE;IAGJ,MAAMC,gBAAgBT,MAAMU,UAAU,IAAI;IAC1C,MAAMC,mBAAmBC,QAAQZ,MAAMa,MAAM,CAACC,MAAM;IACpD,MAAMrB,YAAYgB,gBACd,UACAE,mBACA,YACA;IACJ,MAAMI,YAAYtB,cAAc;IAEhC,MAAMuB,mBAAmBxB,qBAAqBC,WAAWK;IAEzD,qBACE;;0BACE,KAACX;gBACCY,eAAeA;gBACfgB,WAAWA;gBACXE,SAASZ;0BAERR,mBAAAA,WAAY;;YAEdkB,0BACC,MAAC/B;;kCACC,KAACM;kCACD,KAACF;kCACD,KAACC;oBAEA2B,mBAAmB,OAAOP,8BACzB,KAACxB;wBACCiC,SAASlB,MAAMU,UAAU;wBACzBS,aAAanB,MAAMmB,WAAW;yBAE9BR,iCACF,KAACzB;wBACCkC,UAAU;wBACVP,QAAQb,MAAMa,MAAM;wBACpBM,aAAanB,MAAMmB,WAAW;wBAC9BE,qBAAqB;yBAErBC;;iBAEJA;;;AAGV"}