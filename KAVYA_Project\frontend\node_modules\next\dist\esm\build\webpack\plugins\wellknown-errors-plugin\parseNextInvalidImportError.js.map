{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNextInvalidImportError.ts"], "names": ["formatModuleTrace", "getModuleTrace", "SimpleWebpackError", "getNextInvalidImportError", "err", "module", "compilation", "compiler", "loaders", "find", "loader", "includes", "moduleTrace", "formattedModuleTrace", "lastInternalFileName", "invalidImportMessage", "message"], "mappings": "AACA,SAASA,iBAAiB,EAAEC,cAAc,QAAQ,mBAAkB;AACpE,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,OAAO,SAASC,0BACdC,GAAU,EACVC,MAAW,EACXC,WAAgC,EAChCC,QAA0B;IAE1B,IAAI;QACF,IACE,CAACF,OAAOG,OAAO,CAACC,IAAI,CAAC,CAACC,SACpBA,OAAOA,MAAM,CAACC,QAAQ,CAAC,yCAEzB;YACA,OAAO;QACT;QAEA,MAAM,EAAEC,WAAW,EAAE,GAAGX,eAAeI,QAAQC,aAAaC;QAC5D,MAAM,EAAEM,oBAAoB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAE,GACxEf,kBAAkBO,UAAUK;QAE9B,OAAO,IAAIV,mBACTY,sBACAV,IAAIY,OAAO,GACTD,uBACA,6CACAF;IAEN,EAAE,OAAM;QACN,OAAO;IACT;AACF"}