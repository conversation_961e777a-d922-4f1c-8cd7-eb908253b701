{"version": 3, "sources": ["../../../src/server/async-storage/request-async-storage-wrapper.ts"], "names": ["FLIGHT_PARAMETERS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MutableRequestCookiesAdapter", "RequestCookiesAdapter", "ResponseCookies", "RequestCookies", "DraftModeProvider", "splitCookiesString", "getHeaders", "headers", "cleaned", "from", "param", "delete", "toString", "toLowerCase", "seal", "getMutableCookies", "onUpdateCookies", "cookies", "wrap", "mergeMiddlewareCookies", "req", "existingCookies", "setCookieValue", "responseHeaders", "Headers", "cookie", "append", "responseCookies", "getAll", "set", "RequestAsyncStorageWrapper", "storage", "res", "renderOpts", "callback", "previewProps", "undefined", "defaultOnUpdateCookies", "<PERSON><PERSON><PERSON><PERSON>", "cache", "store", "requestCookies", "mutableCookies", "draftMode", "reactLoadableManifest", "assetPrefix", "run"], "mappings": "AASA,SAASA,iBAAiB,QAAQ,6CAA4C;AAC9E,SACEC,cAAc,QAET,yCAAwC;AAC/C,SACEC,4BAA4B,EAC5BC,qBAAqB,QAEhB,iDAAgD;AACvD,SAASC,eAAe,EAAEC,cAAc,QAAQ,gCAA+B;AAC/E,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,kBAAkB,QAAQ,eAAc;AAEjD,SAASC,WAAWC,OAAsC;IACxD,MAAMC,UAAUT,eAAeU,IAAI,CAACF;IACpC,KAAK,MAAMG,SAASZ,kBAAmB;QACrCU,QAAQG,MAAM,CAACD,MAAME,QAAQ,GAAGC,WAAW;IAC7C;IAEA,OAAOd,eAAee,IAAI,CAACN;AAC7B;AAEA,SAASO,kBACPR,OAAsC,EACtCS,eAA6C;IAE7C,MAAMC,UAAU,IAAId,eAAeJ,eAAeU,IAAI,CAACF;IACvD,OAAOP,6BAA6BkB,IAAI,CAACD,SAASD;AACpD;AAQA;;;;CAIC,GACD,SAASG,uBACPC,GAA0B,EAC1BC,eAAiD;IAEjD,IACE,6BAA6BD,IAAIb,OAAO,IACxC,OAAOa,IAAIb,OAAO,CAAC,0BAA0B,KAAK,UAClD;QACA,MAAMe,iBAAiBF,IAAIb,OAAO,CAAC,0BAA0B;QAC7D,MAAMgB,kBAAkB,IAAIC;QAE5B,KAAK,MAAMC,UAAUpB,mBAAmBiB,gBAAiB;YACvDC,gBAAgBG,MAAM,CAAC,cAAcD;QACvC;QAEA,MAAME,kBAAkB,IAAIzB,gBAAgBqB;QAE5C,0DAA0D;QAC1D,KAAK,MAAME,UAAUE,gBAAgBC,MAAM,GAAI;YAC7CP,gBAAgBQ,GAAG,CAACJ;QACtB;IACF;AACF;AAEA,OAAO,MAAMK,6BAGT;IACF;;;;;;;;GAQC,GACDZ,MACEa,OAAwC,EACxC,EAAEX,GAAG,EAAEY,GAAG,EAAEC,UAAU,EAAkB,EACxCC,QAAyC;QAEzC,IAAIC,eAA8CC;QAElD,IAAIH,cAAc,kBAAkBA,YAAY;YAC9C,yDAAyD;YACzDE,eAAe,AAACF,WAAmBE,YAAY;QACjD;QAEA,SAASE,uBAAuBpB,OAAiB;YAC/C,IAAIe,KAAK;gBACPA,IAAIM,SAAS,CAAC,cAAcrB;YAC9B;QACF;QAEA,MAAMsB,QAKF,CAAC;QAEL,MAAMC,QAAsB;YAC1B,IAAIjC,WAAU;gBACZ,IAAI,CAACgC,MAAMhC,OAAO,EAAE;oBAClB,oEAAoE;oBACpE,8BAA8B;oBAC9BgC,MAAMhC,OAAO,GAAGD,WAAWc,IAAIb,OAAO;gBACxC;gBAEA,OAAOgC,MAAMhC,OAAO;YACtB;YACA,IAAIU,WAAU;gBACZ,IAAI,CAACsB,MAAMtB,OAAO,EAAE;oBAClB,4DAA4D;oBAC5D,2DAA2D;oBAC3D,MAAMwB,iBAAiB,IAAItC,eACzBJ,eAAeU,IAAI,CAACW,IAAIb,OAAO;oBAGjCY,uBAAuBC,KAAKqB;oBAE5B,oEAAoE;oBACpE,8BAA8B;oBAC9BF,MAAMtB,OAAO,GAAGhB,sBAAsBa,IAAI,CAAC2B;gBAC7C;gBAEA,OAAOF,MAAMtB,OAAO;YACtB;YACA,IAAIyB,kBAAiB;gBACnB,IAAI,CAACH,MAAMG,cAAc,EAAE;oBACzB,MAAMA,iBAAiB3B,kBACrBK,IAAIb,OAAO,EACX0B,CAAAA,8BAAAA,WAAYjB,eAAe,KACxBgB,CAAAA,MAAMK,yBAAyBD,SAAQ;oBAG5CjB,uBAAuBC,KAAKsB;oBAE5BH,MAAMG,cAAc,GAAGA;gBACzB;gBACA,OAAOH,MAAMG,cAAc;YAC7B;YACA,IAAIC,aAAY;gBACd,IAAI,CAACJ,MAAMI,SAAS,EAAE;oBACpBJ,MAAMI,SAAS,GAAG,IAAIvC,kBACpB+B,cACAf,KACA,IAAI,CAACH,OAAO,EACZ,IAAI,CAACyB,cAAc;gBAEvB;gBAEA,OAAOH,MAAMI,SAAS;YACxB;YACAC,uBAAuBX,CAAAA,8BAAAA,WAAYW,qBAAqB,KAAI,CAAC;YAC7DC,aAAaZ,CAAAA,8BAAAA,WAAYY,WAAW,KAAI;QAC1C;QAEA,OAAOd,QAAQe,GAAG,CAACN,OAAON,UAAUM;IACtC;AACF,EAAC"}