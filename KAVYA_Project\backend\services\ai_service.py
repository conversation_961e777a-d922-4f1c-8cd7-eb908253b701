"""
KAVYA AMR System - AI Service
Core AI service for pathogen identification and resistance prediction
"""

import asyncio
import logging
import json
import base64
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import uuid
import aiohttp
import cv2
from PIL import Image
import io

from ..core.config import get_settings
from ..models.schemas import DiagnosticRequest, TreatmentRecommendation

logger = logging.getLogger(__name__)
settings = get_settings()


class AIService:
    """Core AI service for diagnostic analysis"""
    
    def __init__(self):
        self.model_server_url = settings.AI_MODEL_SERVER_URL
        self.confidence_threshold = settings.AI_CONFIDENCE_THRESHOLD
        self.max_retries = 3
        
    async def analyze_sample(
        self,
        sample_data: Optional[str] = None,
        image_data: Optional[str] = None,
        clinical_data: Optional[Dict[str, Any]] = None,
        patient_id: str = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive AI analysis on sample data
        """
        try:
            analysis_id = str(uuid.uuid4())
            start_time = datetime.utcnow()
            
            logger.info(f"Starting AI analysis {analysis_id} for patient {patient_id}")
            
            # Initialize results structure
            results = {
                "analysis_id": analysis_id,
                "patient_id": patient_id,
                "timestamp": start_time,
                "pathogen_identification": {},
                "resistance_profile": {},
                "biomarker_analysis": {},
                "confidence_score": 0.0,
                "processing_time": 0.0
            }
            
            # Process microscopy image if provided
            if image_data:
                vision_results = await self._analyze_microscopy_image(image_data)
                results["pathogen_identification"].update(vision_results)
                logger.info(f"Vision analysis completed for {analysis_id}")
            
            # Process genomic data if provided
            if sample_data:
                genomic_results = await self._analyze_genomic_data(sample_data)
                results["resistance_profile"].update(genomic_results)
                logger.info(f"Genomic analysis completed for {analysis_id}")
            
            # Process clinical biomarkers
            if clinical_data:
                biomarker_results = await self._analyze_biomarkers(clinical_data)
                results["biomarker_analysis"].update(biomarker_results)
                logger.info(f"Biomarker analysis completed for {analysis_id}")
            
            # Perform multi-modal fusion if multiple data types available
            if len([x for x in [image_data, sample_data, clinical_data] if x]) > 1:
                fusion_results = await self._multimodal_fusion(results)
                results.update(fusion_results)
                logger.info(f"Multi-modal fusion completed for {analysis_id}")
            
            # Calculate overall confidence score
            results["confidence_score"] = self._calculate_confidence_score(results)
            
            # Calculate processing time
            end_time = datetime.utcnow()
            results["processing_time"] = (end_time - start_time).total_seconds()
            
            logger.info(f"AI analysis {analysis_id} completed in {results['processing_time']:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"AI analysis failed: {str(e)}")
            raise
    
    async def _analyze_microscopy_image(self, image_data: str) -> Dict[str, Any]:
        """Analyze microscopy image using computer vision models"""
        try:
            # Decode base64 image
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
            
            # Convert to numpy array
            image_array = np.array(image)
            
            # Call vision model service
            async with aiohttp.ClientSession() as session:
                payload = {
                    "image": image_data,
                    "model_type": "pathogen_classifier",
                    "return_attention": True
                }
                
                async with session.post(
                    f"{self.model_server_url}/vision/analyze",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "primary_pathogen": result.get("primary_pathogen", "Unknown"),
                            "confidence": result.get("confidence", 0.0),
                            "alternative_pathogens": result.get("top_5_predictions", []),
                            "gram_stain": result.get("gram_stain", {}).get("prediction"),
                            "morphology": result.get("morphology", {}).get("prediction"),
                            "biofilm_present": result.get("biofilm_detection", {}).get("present"),
                            "attention_map": result.get("attention_map"),
                            "uncertainty_score": result.get("uncertainty_score", 0.0)
                        }
                    else:
                        logger.error(f"Vision model service error: {response.status}")
                        return self._get_fallback_vision_results()
                        
        except Exception as e:
            logger.error(f"Microscopy image analysis failed: {str(e)}")
            return self._get_fallback_vision_results()
    
    async def _analyze_genomic_data(self, sample_data: str) -> Dict[str, Any]:
        """Analyze genomic data for resistance genes"""
        try:
            # Call genomic model service
            async with aiohttp.ClientSession() as session:
                payload = {
                    "sequence_data": sample_data,
                    "model_type": "genomic_transformer",
                    "analysis_type": "resistance_prediction"
                }
                
                async with session.post(
                    f"{self.model_server_url}/genomics/analyze",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "resistant_antibiotics": result.get("resistant_antibiotics", []),
                            "susceptible_antibiotics": result.get("susceptible_antibiotics", []),
                            "resistance_genes": result.get("resistance_genes", []),
                            "resistance_mechanisms": result.get("resistance_mechanisms", []),
                            "novel_resistance_detected": result.get("novel_resistance_detected", False),
                            "confidence_scores": result.get("confidence_scores", {}),
                            "phylogenetic_analysis": result.get("phylogenetic_analysis", {})
                        }
                    else:
                        logger.error(f"Genomic model service error: {response.status}")
                        return self._get_fallback_genomic_results()
                        
        except Exception as e:
            logger.error(f"Genomic data analysis failed: {str(e)}")
            return self._get_fallback_genomic_results()
    
    async def _analyze_biomarkers(self, clinical_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze clinical biomarkers"""
        try:
            # Extract biomarker values
            biomarkers = clinical_data.get("biomarkers", {})
            
            # Call biomarker analysis service
            async with aiohttp.ClientSession() as session:
                payload = {
                    "biomarkers": biomarkers,
                    "clinical_metadata": clinical_data,
                    "model_type": "biomarker_analyzer"
                }
                
                async with session.post(
                    f"{self.model_server_url}/biomarkers/analyze",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "infection_severity": result.get("infection_severity", "moderate"),
                            "immune_response": result.get("immune_response", {}),
                            "inflammatory_markers": result.get("inflammatory_markers", {}),
                            "organ_function": result.get("organ_function", {}),
                            "prognosis_score": result.get("prognosis_score", 0.5)
                        }
                    else:
                        logger.error(f"Biomarker analysis service error: {response.status}")
                        return self._get_fallback_biomarker_results()
                        
        except Exception as e:
            logger.error(f"Biomarker analysis failed: {str(e)}")
            return self._get_fallback_biomarker_results()
    
    async def _multimodal_fusion(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Perform multi-modal fusion of analysis results"""
        try:
            # Call fusion model service
            async with aiohttp.ClientSession() as session:
                payload = {
                    "vision_results": results.get("pathogen_identification", {}),
                    "genomic_results": results.get("resistance_profile", {}),
                    "biomarker_results": results.get("biomarker_analysis", {}),
                    "model_type": "multimodal_fusion"
                }
                
                async with session.post(
                    f"{self.model_server_url}/fusion/analyze",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "fused_pathogen_confidence": result.get("fused_confidence", 0.0),
                            "consensus_pathogen": result.get("consensus_pathogen"),
                            "resistance_confidence": result.get("resistance_confidence", 0.0),
                            "treatment_urgency": result.get("treatment_urgency", "moderate"),
                            "fusion_quality_score": result.get("fusion_quality", 0.0)
                        }
                    else:
                        logger.error(f"Fusion model service error: {response.status}")
                        return {}
                        
        except Exception as e:
            logger.error(f"Multi-modal fusion failed: {str(e)}")
            return {}
    
    def _calculate_confidence_score(self, results: Dict[str, Any]) -> float:
        """Calculate overall confidence score"""
        try:
            scores = []
            
            # Vision confidence
            if "pathogen_identification" in results:
                vision_conf = results["pathogen_identification"].get("confidence", 0.0)
                scores.append(vision_conf)
            
            # Genomic confidence
            if "resistance_profile" in results:
                genomic_conf = results["resistance_profile"].get("confidence_scores", {})
                if genomic_conf:
                    avg_genomic = np.mean(list(genomic_conf.values()))
                    scores.append(avg_genomic)
            
            # Fusion confidence
            if "fused_pathogen_confidence" in results:
                fusion_conf = results["fused_pathogen_confidence"]
                scores.append(fusion_conf)
            
            # Calculate weighted average
            if scores:
                return float(np.mean(scores))
            else:
                return 0.5  # Default moderate confidence
                
        except Exception as e:
            logger.error(f"Confidence calculation failed: {str(e)}")
            return 0.5
    
    async def generate_treatment_recommendations(
        self,
        analysis_results: Dict[str, Any],
        clinical_data: Optional[Dict[str, Any]] = None
    ) -> TreatmentRecommendation:
        """Generate evidence-based treatment recommendations"""
        try:
            # Extract key information
            pathogen = analysis_results.get("pathogen_identification", {}).get("primary_pathogen", "Unknown")
            resistance_profile = analysis_results.get("resistance_profile", {})
            biomarker_analysis = analysis_results.get("biomarker_analysis", {})
            
            # Call treatment recommendation service
            async with aiohttp.ClientSession() as session:
                payload = {
                    "pathogen": pathogen,
                    "resistance_profile": resistance_profile,
                    "biomarker_analysis": biomarker_analysis,
                    "clinical_data": clinical_data or {},
                    "model_type": "treatment_optimizer"
                }
                
                async with session.post(
                    f"{self.model_server_url}/treatment/recommend",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return TreatmentRecommendation(
                            primary_therapy=result.get("primary_therapy", "Empirical therapy"),
                            alternative_therapies=result.get("alternative_therapies", []),
                            dosage_regimen=result.get("dosage_regimen", {}),
                            duration_days=result.get("duration_days"),
                            monitoring_parameters=result.get("monitoring_parameters", []),
                            contraindications=result.get("contraindications", []),
                            nanobot_therapy_recommended=result.get("nanobot_therapy_recommended", False)
                        )
                    else:
                        logger.error(f"Treatment recommendation service error: {response.status}")
                        return self._get_fallback_treatment_recommendation(pathogen)
                        
        except Exception as e:
            logger.error(f"Treatment recommendation failed: {str(e)}")
            return self._get_fallback_treatment_recommendation("Unknown")
    
    def _get_fallback_vision_results(self) -> Dict[str, Any]:
        """Fallback results for vision analysis"""
        return {
            "primary_pathogen": "Unknown",
            "confidence": 0.5,
            "alternative_pathogens": [],
            "gram_stain": "Unknown",
            "morphology": "Unknown",
            "biofilm_present": False,
            "uncertainty_score": 0.8
        }
    
    def _get_fallback_genomic_results(self) -> Dict[str, Any]:
        """Fallback results for genomic analysis"""
        return {
            "resistant_antibiotics": [],
            "susceptible_antibiotics": [],
            "resistance_genes": [],
            "resistance_mechanisms": [],
            "novel_resistance_detected": False,
            "confidence_scores": {}
        }
    
    def _get_fallback_biomarker_results(self) -> Dict[str, Any]:
        """Fallback results for biomarker analysis"""
        return {
            "infection_severity": "moderate",
            "immune_response": {},
            "inflammatory_markers": {},
            "organ_function": {},
            "prognosis_score": 0.5
        }
    
    def _get_fallback_treatment_recommendation(self, pathogen: str) -> TreatmentRecommendation:
        """Fallback treatment recommendation"""
        return TreatmentRecommendation(
            primary_therapy="Empirical broad-spectrum antibiotic therapy",
            alternative_therapies=["Consult infectious disease specialist"],
            monitoring_parameters=["Clinical response", "Laboratory parameters"],
            contraindications=["Known drug allergies"],
            nanobot_therapy_recommended=False
        )
    
    async def health_check(self) -> Dict[str, str]:
        """Health check for AI service"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.model_server_url}/health",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        return {"status": "healthy", "model_server": "connected"}
                    else:
                        return {"status": "degraded", "model_server": "error"}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    async def preprocess_image(self, image_data: bytes) -> str:
        """Preprocess uploaded image"""
        try:
            # Convert to PIL Image
            image = Image.open(io.BytesIO(image_data))
            
            # Resize and normalize
            image = image.resize((224, 224))
            
            # Convert back to base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            encoded_image = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return encoded_image
            
        except Exception as e:
            logger.error(f"Image preprocessing failed: {str(e)}")
            raise
    
    async def store_image(self, processed_image: str, patient_id: str, user_id: str) -> str:
        """Store processed image and return reference ID"""
        try:
            image_id = str(uuid.uuid4())
            # In production, store in cloud storage (S3, GCS, etc.)
            # For now, return the generated ID
            logger.info(f"Image stored with ID: {image_id}")
            return image_id
        except Exception as e:
            logger.error(f"Image storage failed: {str(e)}")
            raise
    
    async def store_analysis_results(self, analysis_result: Dict[str, Any], patient_id: str, user_id: str):
        """Store analysis results in database"""
        try:
            # In production, store in database
            logger.info(f"Analysis results stored for patient {patient_id}")
        except Exception as e:
            logger.error(f"Analysis storage failed: {str(e)}")
    
    async def create_patient_record(self, patient_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Create encrypted patient record"""
        try:
            # In production, encrypt sensitive data and store in database
            patient_record = {
                "patient_id": patient_data["patient_id"],
                "created_at": datetime.utcnow(),
                "consent_status": patient_data.get("consent_given", False),
                "anonymization_level": "standard"
            }
            logger.info(f"Patient record created: {patient_data['patient_id']}")
            return patient_record
        except Exception as e:
            logger.error(f"Patient record creation failed: {str(e)}")
            raise
    
    async def get_patient_analyses(self, patient_id: str, limit: int, user_id: str) -> List[Dict[str, Any]]:
        """Get patient's analysis history"""
        try:
            # In production, query database for patient analyses
            # Return mock data for now
            analyses = [
                {
                    "analysis_id": str(uuid.uuid4()),
                    "analysis_type": "diagnostic",
                    "created_at": datetime.utcnow() - timedelta(days=1),
                    "pathogen_identified": "Staphylococcus aureus",
                    "resistance_detected": True,
                    "confidence_score": 0.92,
                    "validation_status": "validated"
                }
            ]
            return analyses[:limit]
        except Exception as e:
            logger.error(f"Patient analyses retrieval failed: {str(e)}")
            raise
