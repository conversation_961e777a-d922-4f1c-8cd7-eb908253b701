{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-url-parser.ts"], "names": ["valueParser", "resolveRequests", "normalizeUrl", "requestify", "isUrlRequestable", "isDataUrl", "WEBPACK_IGNORE_COMMENT_REGEXP", "isUrlFunc", "isImageSetFunc", "needParseDeclaration", "getNodeFromUrlFunc", "node", "nodes", "getWebpackIgnoreCommentValue", "index", "inBetween", "prevValueNode", "type", "matched", "value", "match", "shouldHandleURL", "url", "declaration", "result", "isSupportDataURLInNewURL", "length", "warn", "toString", "decodeURIComponent", "ignoreError", "parseDeclaration", "key", "test", "parsed", "raws", "raw", "between", "lastCommentIndex", "lastIndexOf", "slice", "isIgnoreOnDeclaration", "prevNode", "prev", "text", "needIgnore", "parsedURLs", "walk", "valueNode", "valueNodes", "undefined", "isStringValue", "stringify", "queryParts", "split", "prefix", "pop", "join", "push", "needQuotes", "innerIndex", "nNode", "entries", "plugin", "options", "postcssPlugin", "prepare", "parsedDeclarations", "Declaration", "parsedURL", "OnceExit", "resolvedDeclarations", "Promise", "all", "map", "parsedDeclaration", "filter", "<PERSON><PERSON><PERSON>", "pathname", "query", "hash<PERSON><PERSON><PERSON><PERSON><PERSON>", "hash", "needToResolveURL", "rootContext", "request", "resolver", "context", "resolvedUrl", "Set", "urlToNameMap", "Map", "urlToReplacementMap", "hasUrlImportHelper", "item", "imports", "importName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "resolve", "newUrl", "get", "size", "set", "JSON", "<PERSON><PERSON><PERSON>", "replacement<PERSON>ame", "replacements", "postcss"], "mappings": "AAAA,OAAOA,iBAAiB,0CAAyC;AAEjE,SACEC,eAAe,EACfC,YAAY,EACZC,UAAU,EACVC,gBAAgB,EAChBC,SAAS,EAETC,AADA,kEAAkE;AAClEA,6BAA6B,QACxB,WAAU;AAEjB,MAAMC,YAAY;AAClB,MAAMC,iBAAiB;AACvB,MAAMC,uBAAuB;AAE7B,SAASC,mBAAmBC,IAAS;IACnC,OAAOA,KAAKC,KAAK,IAAID,KAAKC,KAAK,CAAC,EAAE;AACpC;AAEA,SAASC,6BAA6BC,KAAU,EAAEF,KAAU,EAAEG,SAAe;IAC3E,IAAID,UAAU,KAAK,OAAOC,cAAc,aAAa;QACnD,OAAOA;IACT;IAEA,IAAIC,gBAAgBJ,KAAK,CAACE,QAAQ,EAAE;IAEpC,IAAI,CAACE,eAAe;QAClB,6CAA6C;QAC7C;IACF;IAEA,IAAIA,cAAcC,IAAI,KAAK,SAAS;QAClC,IAAI,CAACL,KAAK,CAACE,QAAQ,EAAE,EAAE;YACrB,6CAA6C;YAC7C;QACF;QAEAE,gBAAgBJ,KAAK,CAACE,QAAQ,EAAE;IAClC;IAEA,IAAIE,cAAcC,IAAI,KAAK,WAAW;QACpC,6CAA6C;QAC7C;IACF;IAEA,MAAMC,UAAUF,cAAcG,KAAK,CAACC,KAAK,CAACd;IAE1C,OAAOY,WAAWA,OAAO,CAAC,EAAE,KAAK;AACnC;AAEA,SAASG,gBACPC,GAAQ,EACRC,WAAgB,EAChBC,MAAW,EACXC,wBAA6B;IAE7B,IAAIH,IAAII,MAAM,KAAK,GAAG;QACpBF,OAAOG,IAAI,CAAC,CAAC,uBAAuB,EAAEJ,YAAYK,QAAQ,GAAG,CAAC,CAAC,EAAE;YAC/DjB,MAAMY;QACR;QAEA,OAAO;IACT;IAEA,IAAIlB,UAAUiB,QAAQG,0BAA0B;QAC9C,IAAI;YACFI,mBAAmBP;QACrB,EAAE,OAAOQ,aAAa;YACpB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,IAAI,CAAC1B,iBAAiBkB,MAAM;QAC1B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASS,iBACPR,WAAgB,EAChBS,GAAQ,EACRR,MAAW,EACXC,wBAA6B;IAE7B,IAAI,CAAChB,qBAAqBwB,IAAI,CAACV,WAAW,CAACS,IAAI,GAAG;QAChD;IACF;IAEA,MAAME,SAASlC,YACbuB,YAAYY,IAAI,IAAIZ,YAAYY,IAAI,CAAChB,KAAK,IAAII,YAAYY,IAAI,CAAChB,KAAK,CAACiB,GAAG,GACpEb,YAAYY,IAAI,CAAChB,KAAK,CAACiB,GAAG,GAC1Bb,WAAW,CAACS,IAAI;IAGtB,IAAIjB;IAEJ,IAAIQ,YAAYY,IAAI,IAAIZ,YAAYY,IAAI,CAACE,OAAO,EAAE;QAChD,MAAMC,mBAAmBf,YAAYY,IAAI,CAACE,OAAO,CAACE,WAAW,CAAC;QAE9D,MAAMrB,UAAUK,YAAYY,IAAI,CAACE,OAAO,CACrCG,KAAK,CAACF,kBACNlB,KAAK,CAACd;QAET,IAAIY,SAAS;YACXH,YAAYG,OAAO,CAAC,EAAE,KAAK;QAC7B;IACF;IAEA,IAAIuB,wBAAwB;IAE5B,MAAMC,WAAWnB,YAAYoB,IAAI;IAEjC,IAAID,YAAYA,SAASzB,IAAI,KAAK,WAAW;QAC3C,MAAMC,UAAUwB,SAASE,IAAI,CAACxB,KAAK,CAACd;QAEpC,IAAIY,SAAS;YACXuB,wBAAwBvB,OAAO,CAAC,EAAE,KAAK;QACzC;IACF;IAEA,IAAI2B;IAEJ,MAAMC,aAAoB,EAAE;IAE5BZ,OAAOa,IAAI,CAAC,CAACC,WAAgBlC,OAAYmC;QACvC,IAAID,UAAU/B,IAAI,KAAK,YAAY;YACjC;QACF;QAEA,IAAIV,UAAU0B,IAAI,CAACe,UAAU7B,KAAK,GAAG;YACnC0B,aAAahC,6BAA6BC,OAAOmC,YAAYlC;YAE7D,IACE,AAAC0B,yBAAyB,OAAOI,eAAe,eAChDA,YACA;gBACA,IAAIA,YAAY;oBACd,wCAAwC;oBACxCA,aAAaK;gBACf;gBAEA;YACF;YAEA,MAAM,EAAEtC,KAAK,EAAE,GAAGoC;YAClB,MAAMG,gBAAgBvC,MAAMc,MAAM,KAAK,KAAKd,KAAK,CAAC,EAAE,CAACK,IAAI,KAAK;YAC9D,IAAIK,MAAM6B,gBAAgBvC,KAAK,CAAC,EAAE,CAACO,KAAK,GAAGnB,YAAYoD,SAAS,CAACxC;YACjEU,MAAMpB,aAAaoB,KAAK6B;YAExB,+BAA+B;YAC/B,IACE,CAAC9B,gBAAgBC,KAAKC,aAAaC,QAAQC,2BAC3C;gBACA,6CAA6C;gBAC7C,OAAO;YACT;YAEA,MAAM4B,aAAa/B,IAAIgC,KAAK,CAAC;YAC7B,IAAIC;YAEJ,IAAIF,WAAW3B,MAAM,GAAG,GAAG;gBACzBJ,MAAM+B,WAAWG,GAAG;gBACpBD,SAASF,WAAWI,IAAI,CAAC;YAC3B;YAEAX,WAAWY,IAAI,CAAC;gBACdnC;gBACAW;gBACAvB,MAAMD,mBAAmBsC;gBACzBO;gBACAjC;gBACAqC,YAAY;YACd;YAEA,6CAA6C;YAC7C,OAAO;QACT,OAAO,IAAInD,eAAeyB,IAAI,CAACe,UAAU7B,KAAK,GAAG;YAC/C,KAAK,MAAM,CAACyC,YAAYC,MAAM,IAAIb,UAAUpC,KAAK,CAACkD,OAAO,GAAI;gBAC3D,MAAM,EAAE7C,IAAI,EAAEE,KAAK,EAAE,GAAG0C;gBAExB,IAAI5C,SAAS,cAAcV,UAAU0B,IAAI,CAACd,QAAQ;oBAChD0B,aAAahC,6BAA6B+C,YAAYZ,UAAUpC,KAAK;oBAErE,IACE,AAAC6B,yBAAyB,OAAOI,eAAe,eAChDA,YACA;wBACA,IAAIA,YAAY;4BACd,wCAAwC;4BACxCA,aAAaK;wBACf;wBAGA;oBACF;oBAEA,MAAM,EAAEtC,KAAK,EAAE,GAAGiD;oBAClB,MAAMV,gBAAgBvC,MAAMc,MAAM,KAAK,KAAKd,KAAK,CAAC,EAAE,CAACK,IAAI,KAAK;oBAC9D,IAAIK,MAAM6B,gBACNvC,KAAK,CAAC,EAAE,CAACO,KAAK,GACdnB,YAAYoD,SAAS,CAACxC;oBAC1BU,MAAMpB,aAAaoB,KAAK6B;oBAExB,+BAA+B;oBAC/B,IACE,CAAC9B,gBAAgBC,KAAKC,aAAaC,QAAQC,2BAC3C;wBACA,6CAA6C;wBAC7C,OAAO;oBACT;oBAEA,MAAM4B,aAAa/B,IAAIgC,KAAK,CAAC;oBAC7B,IAAIC;oBAEJ,IAAIF,WAAW3B,MAAM,GAAG,GAAG;wBACzBJ,MAAM+B,WAAWG,GAAG;wBACpBD,SAASF,WAAWI,IAAI,CAAC;oBAC3B;oBAEAX,WAAWY,IAAI,CAAC;wBACdnC;wBACAW;wBACAvB,MAAMD,mBAAmBmD;wBACzBN;wBACAjC;wBACAqC,YAAY;oBACd;gBACF,OAAO,IAAI1C,SAAS,UAAU;oBAC5B4B,aAAahC,6BAA6B+C,YAAYZ,UAAUpC,KAAK;oBAErE,IACE,AAAC6B,yBAAyB,OAAOI,eAAe,eAChDA,YACA;wBACA,IAAIA,YAAY;4BACd,wCAAwC;4BACxCA,aAAaK;wBACf;wBAGA;oBACF;oBAEA,IAAI5B,MAAMpB,aAAaiB,OAAO;oBAE9B,+BAA+B;oBAC/B,IACE,CAACE,gBAAgBC,KAAKC,aAAaC,QAAQC,2BAC3C;wBACA,6CAA6C;wBAC7C,OAAO;oBACT;oBAEA,MAAM4B,aAAa/B,IAAIgC,KAAK,CAAC;oBAC7B,IAAIC;oBAEJ,IAAIF,WAAW3B,MAAM,GAAG,GAAG;wBACzBJ,MAAM+B,WAAWG,GAAG;wBACpBD,SAASF,WAAWI,IAAI,CAAC;oBAC3B;oBAEAX,WAAWY,IAAI,CAAC;wBACdnC;wBACAW;wBACAvB,MAAMkD;wBACNN;wBACAjC;wBACAqC,YAAY;oBACd;gBACF;YACF;YAEA,qCAAqC;YACrC,6CAA6C;YAC7C,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7C,OAAOb;AACT;AAEA,MAAMiB,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACfC,SAAQ1C,MAAW;YACjB,MAAM2C,qBAA4B,EAAE;YAEpC,OAAO;gBACLC,aAAY7C,WAAgB;oBAC1B,MAAM,EAAEE,wBAAwB,EAAE,GAAGuC;oBACrC,MAAMK,YAAYtC,iBAChBR,aACA,SACAC,QACAC;oBAGF,IAAI,CAAC4C,WAAW;wBACd;oBACF;oBAEAF,mBAAmBT,IAAI,IAAIW;gBAC7B;gBACA,MAAMC;oBACJ,IAAIH,mBAAmBzC,MAAM,KAAK,GAAG;wBACnC;oBACF;oBAEA,MAAM6C,uBAAuB,MAAMC,QAAQC,GAAG,CAC5CN,mBAAmBO,GAAG,CAAC,OAAOC;wBAC5B,MAAM,EAAErD,GAAG,EAAE,GAAGqD;wBAEhB,IAAIX,QAAQY,MAAM,EAAE;4BAClB,MAAMC,WAAW,MAAMb,QAAQY,MAAM,CAACtD;4BAEtC,IAAI,CAACuD,UAAU;gCACb,6CAA6C;gCAC7C;4BACF;wBACF;wBAEA,IAAIxE,UAAUiB,MAAM;4BAClB,6CAA6C;4BAC7C,OAAOqD;wBACT;wBAEA,MAAM,CAACG,UAAUC,OAAOC,YAAY,GAAG1D,IAAIgC,KAAK,CAAC,UAAU;wBAE3D,IAAI2B,OAAOF,QAAQ,MAAM;wBACzBE,QAAQD,cAAc,CAAC,CAAC,EAAEA,YAAY,CAAC,GAAG;wBAE1C,MAAM,EAAEE,gBAAgB,EAAEC,WAAW,EAAE,GAAGnB;wBAC1C,MAAMoB,UAAUjF,WACd2E,UACAK,aACA,mDAAmD;wBACnDD;wBAGF,IAAI,CAACA,kBAAkB;4BACrB,6CAA6C;4BAC7C,OAAO;gCAAE,GAAGP,iBAAiB;gCAAErD,KAAK8D;gCAASH;4BAAK;wBACpD;wBAEA,MAAM,EAAEI,QAAQ,EAAEC,OAAO,EAAE,GAAGtB;wBAC9B,MAAMuB,cAAc,MAAMtF,gBAAgBoF,UAAUC,SAAS;+BACxD,IAAIE,IAAI;gCAACJ;gCAAS9D;6BAAI;yBAC1B;wBAED,IAAI,CAACiE,aAAa;4BAChB,6CAA6C;4BAC7C;wBACF;wBAEA,6CAA6C;wBAC7C,OAAO;4BAAE,GAAGZ,iBAAiB;4BAAErD,KAAKiE;4BAAaN;wBAAK;oBACxD;oBAGF,MAAMQ,eAAe,IAAIC;oBACzB,MAAMC,sBAAsB,IAAID;oBAEhC,IAAIE,qBAAqB;oBAEzB,IACE,IAAI9E,QAAQ,GACZA,SAASyD,qBAAqB7C,MAAM,GAAG,GACvCZ,QACA;wBACA,MAAM+E,OAAOtB,oBAAoB,CAACzD,MAAM;wBAExC,IAAI,CAAC+E,MAAM;4BAET;wBACF;wBAEA,IAAI,CAACD,oBAAoB;4BACvB5B,QAAQ8B,OAAO,CAACpC,IAAI,CAAC;gCACnBzC,MAAM;gCACN8E,YAAY;gCACZzE,KAAK0C,QAAQgC,UAAU,CACrBC,QAAQC,OAAO,CAAC;gCAElBpF,OAAO,CAAC;4BACV;4BAEA8E,qBAAqB;wBACvB;wBAEA,MAAM,EAAEtE,GAAG,EAAEiC,MAAM,EAAE,GAAGsC;wBACxB,MAAMM,SAAS5C,SAAS,CAAC,EAAEA,OAAO,CAAC,EAAEjC,IAAI,CAAC,GAAGA;wBAC7C,IAAIyE,aAAaN,aAAaW,GAAG,CAACD;wBAElC,IAAI,CAACJ,YAAY;4BACfA,aAAa,CAAC,yBAAyB,EAAEN,aAAaY,IAAI,CAAC,GAAG,CAAC;4BAC/DZ,aAAaa,GAAG,CAACH,QAAQJ;4BAEzB/B,QAAQ8B,OAAO,CAACpC,IAAI,CAAC;gCACnBzC,MAAM;gCACN8E;gCACAzE,KAAK0C,QAAQkB,gBAAgB,GACzBlB,QAAQgC,UAAU,CAACG,UACnBI,KAAKnD,SAAS,CAAC+C;gCACnBrF;4BACF;wBACF;wBAEA,MAAM,EAAEmE,IAAI,EAAEtB,UAAU,EAAE,GAAGkC;wBAC7B,MAAMW,iBAAiBD,KAAKnD,SAAS,CAAC;4BAAE+C;4BAAQlB;4BAAMtB;wBAAW;wBACjE,IAAI8C,kBAAkBd,oBAAoBS,GAAG,CAACI;wBAE9C,IAAI,CAACC,iBAAiB;4BACpBA,kBAAkB,CAAC,8BAA8B,EAAEd,oBAAoBU,IAAI,CAAC,GAAG,CAAC;4BAChFV,oBAAoBW,GAAG,CAACE,gBAAgBC;4BAExCzC,QAAQ0C,YAAY,CAAChD,IAAI,CAAC;gCACxB+C;gCACAV;gCACAd;gCACAtB;4BACF;wBACF;wBAEA,6CAA6C;wBAC7CkC,KAAKlF,IAAI,CAACM,IAAI,GAAG;wBACjB,6CAA6C;wBAC7C4E,KAAKlF,IAAI,CAACQ,KAAK,GAAGsF;wBAClB,6CAA6C;wBAC7CZ,KAAKtE,WAAW,CAACJ,KAAK,GAAG0E,KAAK3D,MAAM,CAACN,QAAQ;oBAC/C;gBACF;YACF;QACF;IACF;AACF;AAEAmC,OAAO4C,OAAO,GAAG;AAEjB,eAAe5C,OAAM"}