"""
KAVYA AMR System - Database Models
SQLAlchemy models for the KAVYA system
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from sqlalchemy import (
    Column, Integer, String, DateTime, Boolean, Float, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session
from sqlalchemy.dialects.postgresql import UUID, ARRAY
import uuid

Base = declarative_base()


class TimestampMixin:
    """Mixin for created_at and updated_at timestamps"""
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                       onupdate=lambda: datetime.now(timezone.utc), nullable=False)


class Patient(Base, TimestampMixin):
    """Patient information table"""
    __tablename__ = "patients"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    patient_id = Column(String(50), unique=True, nullable=False, index=True)
    
    # Demographics
    age = Column(Integer)
    gender = Column(String(10))
    ethnicity = Column(String(50))
    
    # Medical History
    medical_history = Column(JSON)
    allergies = Column(ARRAY(String))
    current_medications = Column(JSON)
    comorbidities = Column(ARRAY(String))
    
    # Contact Information (encrypted)
    contact_info = Column(JSON)  # Encrypted patient contact details
    
    # Privacy & Consent
    consent_status = Column(Boolean, default=False)
    consent_date = Column(DateTime)
    data_retention_until = Column(DateTime)
    anonymization_level = Column(String(20), default="standard")
    
    # Relationships
    samples = relationship("Sample", back_populates="patient")
    analyses = relationship("Analysis", back_populates="patient")
    treatments = relationship("Treatment", back_populates="patient")
    
    __table_args__ = (
        Index('idx_patient_demographics', 'age', 'gender', 'ethnicity'),
        CheckConstraint('age >= 0 AND age <= 150', name='check_age_range'),
    )


class Sample(Base, TimestampMixin):
    """Sample collection and metadata"""
    __tablename__ = "samples"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sample_id = Column(String(50), unique=True, nullable=False, index=True)
    patient_id = Column(UUID(as_uuid=True), ForeignKey("patients.id"), nullable=False)
    
    # Sample Information
    sample_type = Column(String(50), nullable=False)  # swab, biopsy, blood, etc.
    collection_site = Column(String(100))  # anatomical location
    collection_date = Column(DateTime, nullable=False)
    collection_method = Column(String(100))
    
    # Sample Quality
    quality_score = Column(Float)
    contamination_level = Column(String(20))
    storage_conditions = Column(JSON)
    
    # Processing Status
    processing_status = Column(String(20), default="collected")
    processed_date = Column(DateTime)
    
    # Metadata
    metadata = Column(JSON)
    
    # Relationships
    patient = relationship("Patient", back_populates="samples")
    analyses = relationship("Analysis", back_populates="sample")
    
    __table_args__ = (
        Index('idx_sample_collection', 'collection_date', 'sample_type'),
        CheckConstraint("quality_score >= 0 AND quality_score <= 1", name='check_quality_score'),
    )


class Analysis(Base, TimestampMixin):
    """AI analysis results"""
    __tablename__ = "analyses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    analysis_id = Column(String(50), unique=True, nullable=False, index=True)
    patient_id = Column(UUID(as_uuid=True), ForeignKey("patients.id"), nullable=False)
    sample_id = Column(UUID(as_uuid=True), ForeignKey("samples.id"), nullable=False)
    
    # Analysis Type
    analysis_type = Column(String(50), nullable=False)  # pathogen_id, resistance, fusion
    analysis_version = Column(String(20))
    
    # AI Model Information
    model_name = Column(String(100))
    model_version = Column(String(20))
    model_confidence = Column(Float)
    
    # Results
    pathogen_identification = Column(JSON)
    resistance_prediction = Column(JSON)
    biomarker_analysis = Column(JSON)
    severity_assessment = Column(JSON)
    
    # Performance Metrics
    processing_time = Column(Float)  # seconds
    accuracy_score = Column(Float)
    uncertainty_score = Column(Float)
    
    # Quality Assurance
    validation_status = Column(String(20), default="pending")
    validated_by = Column(String(100))
    validation_date = Column(DateTime)
    
    # Relationships
    patient = relationship("Patient", back_populates="analyses")
    sample = relationship("Sample", back_populates="analyses")
    treatments = relationship("Treatment", back_populates="analysis")
    
    __table_args__ = (
        Index('idx_analysis_results', 'analysis_type', 'model_confidence'),
        CheckConstraint("model_confidence >= 0 AND model_confidence <= 1", name='check_confidence'),
    )


class Treatment(Base, TimestampMixin):
    """Treatment plans and outcomes"""
    __tablename__ = "treatments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    treatment_id = Column(String(50), unique=True, nullable=False, index=True)
    patient_id = Column(UUID(as_uuid=True), ForeignKey("patients.id"), nullable=False)
    analysis_id = Column(UUID(as_uuid=True), ForeignKey("analyses.id"), nullable=False)
    
    # Treatment Plan
    therapy_type = Column(String(50))  # antibiotic, nanobot, combination
    primary_therapy = Column(String(100))
    alternative_therapies = Column(ARRAY(String))
    dosage_regimen = Column(JSON)
    
    # Nanobot Therapy (if applicable)
    nanobot_deployment_id = Column(String(50))
    nanobot_config = Column(JSON)
    target_site = Column(JSON)
    
    # Treatment Timeline
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    duration_days = Column(Integer)
    
    # Outcomes
    treatment_response = Column(String(20))  # cured, improved, failed, ongoing
    side_effects = Column(ARRAY(String))
    resistance_development = Column(Boolean, default=False)
    
    # Follow-up
    follow_up_required = Column(Boolean, default=True)
    next_assessment_date = Column(DateTime)
    
    # Relationships
    patient = relationship("Patient", back_populates="treatments")
    analysis = relationship("Analysis", back_populates="treatments")
    nanobots = relationship("NanobotDeployment", back_populates="treatment")
    
    __table_args__ = (
        Index('idx_treatment_outcomes', 'treatment_response', 'therapy_type'),
    )


class NanobotDeployment(Base, TimestampMixin):
    """Nanobot deployment and monitoring"""
    __tablename__ = "nanobot_deployments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    deployment_id = Column(String(50), unique=True, nullable=False, index=True)
    treatment_id = Column(UUID(as_uuid=True), ForeignKey("treatments.id"), nullable=False)
    
    # Deployment Configuration
    nanobot_type = Column(String(50))
    payload_type = Column(String(50))
    payload_concentration = Column(Float)
    target_pathogen = Column(String(100))
    
    # Navigation Plan
    navigation_strategy = Column(String(50))
    target_coordinates = Column(JSON)
    waypoints = Column(JSON)
    estimated_delivery_time = Column(Float)  # minutes
    
    # Deployment Status
    deployment_status = Column(String(20), default="planned")
    deployment_date = Column(DateTime)
    completion_date = Column(DateTime)
    
    # Monitoring Data
    real_time_location = Column(JSON)
    payload_release_status = Column(JSON)
    biocompatibility_score = Column(Float)
    efficacy_metrics = Column(JSON)
    
    # Safety Monitoring
    adverse_events = Column(ARRAY(String))
    safety_alerts = Column(JSON)
    emergency_stop_triggered = Column(Boolean, default=False)
    
    # Relationships
    treatment = relationship("Treatment", back_populates="nanobots")
    
    __table_args__ = (
        Index('idx_nanobot_status', 'deployment_status', 'deployment_date'),
        CheckConstraint("biocompatibility_score >= 0 AND biocompatibility_score <= 1", 
                       name='check_biocompatibility'),
    )


class GlobalSurveillance(Base, TimestampMixin):
    """Global AMR surveillance data"""
    __tablename__ = "global_surveillance"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Geographic Information
    country = Column(String(100))
    region = Column(String(100))
    institution = Column(String(200))
    latitude = Column(Float)
    longitude = Column(Float)
    
    # Temporal Information
    reporting_period = Column(String(20))  # monthly, quarterly, yearly
    data_date = Column(DateTime, nullable=False)
    
    # Pathogen Data
    pathogen_species = Column(String(100))
    resistance_profile = Column(JSON)
    prevalence_rate = Column(Float)
    trend_direction = Column(String(20))  # increasing, decreasing, stable
    
    # Outbreak Information
    outbreak_status = Column(Boolean, default=False)
    outbreak_severity = Column(String(20))
    cases_reported = Column(Integer)
    
    # Data Quality
    data_completeness = Column(Float)
    data_source = Column(String(100))
    validation_status = Column(String(20))
    
    # Privacy Protection
    anonymized = Column(Boolean, default=True)
    aggregation_level = Column(String(20))  # individual, institutional, regional
    
    __table_args__ = (
        Index('idx_surveillance_geo_temporal', 'country', 'region', 'data_date'),
        Index('idx_surveillance_pathogen', 'pathogen_species', 'outbreak_status'),
        CheckConstraint("prevalence_rate >= 0 AND prevalence_rate <= 1", name='check_prevalence'),
    )


class AuditLog(Base, TimestampMixin):
    """Audit logging for compliance"""
    __tablename__ = "audit_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User Information
    user_id = Column(String(100))
    user_role = Column(String(50))
    session_id = Column(String(100))
    
    # Action Information
    action_type = Column(String(50), nullable=False)  # create, read, update, delete
    resource_type = Column(String(50), nullable=False)  # patient, sample, analysis
    resource_id = Column(String(100))
    
    # Request Details
    endpoint = Column(String(200))
    method = Column(String(10))
    ip_address = Column(String(45))
    user_agent = Column(Text)
    
    # Data Changes
    old_values = Column(JSON)
    new_values = Column(JSON)
    
    # Compliance
    hipaa_logged = Column(Boolean, default=True)
    gdpr_logged = Column(Boolean, default=True)
    retention_until = Column(DateTime)
    
    __table_args__ = (
        Index('idx_audit_user_action', 'user_id', 'action_type', 'created_at'),
        Index('idx_audit_resource', 'resource_type', 'resource_id'),
    )


class ModelPerformance(Base, TimestampMixin):
    """AI model performance tracking"""
    __tablename__ = "model_performance"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Model Information
    model_name = Column(String(100), nullable=False)
    model_version = Column(String(20), nullable=False)
    model_type = Column(String(50))  # pathogen_id, resistance, fusion
    
    # Performance Metrics
    accuracy = Column(Float)
    precision = Column(Float)
    recall = Column(Float)
    f1_score = Column(Float)
    auc_roc = Column(Float)
    
    # Evaluation Dataset
    dataset_name = Column(String(100))
    dataset_size = Column(Integer)
    evaluation_date = Column(DateTime, nullable=False)
    
    # Deployment Metrics
    inference_time_ms = Column(Float)
    memory_usage_mb = Column(Float)
    throughput_per_second = Column(Float)
    
    # Bias and Fairness
    demographic_parity = Column(Float)
    equalized_odds = Column(Float)
    bias_metrics = Column(JSON)
    
    __table_args__ = (
        Index('idx_model_performance', 'model_name', 'model_version', 'evaluation_date'),
        UniqueConstraint('model_name', 'model_version', 'dataset_name', 'evaluation_date',
                        name='uq_model_evaluation'),
    )


# Database utility functions
def create_tables(engine):
    """Create all tables"""
    Base.metadata.create_all(bind=engine)


def drop_tables(engine):
    """Drop all tables"""
    Base.metadata.drop_all(bind=engine)
