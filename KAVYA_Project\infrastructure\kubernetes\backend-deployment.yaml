apiVersion: apps/v1
kind: Deployment
metadata:
  name: kavya-backend
  namespace: kavya-amr
  labels:
    app: kavya-backend
    component: api
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: kavya-backend
  template:
    metadata:
      labels:
        app: kavya-backend
        component: api
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: kavya-backend-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: kavya-backend
        image: kavya/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: kavya-secrets
              key: database-url
        - name: MONGODB_URL
          valueFrom:
            secretKeyRef:
              name: kavya-secrets
              key: mongodb-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: kavya-secrets
              key: redis-url
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: kavya-secrets
              key: jwt-secret
        - name: API_PREFIX
          value: "/api/v1"
        - name: LOG_LEVEL
          value: "INFO"
        - name: ENABLE_CORS
          value: "true"
        - name: PROMETHEUS_METRICS_PATH
          value: "/metrics"
        envFrom:
        - configMapRef:
            name: kavya-config
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: models-storage
          mountPath: /app/models
          readOnly: true
        - name: data-storage
          mountPath: /app/data
        - name: logs-storage
          mountPath: /app/logs
        - name: config-volume
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: models-storage
        persistentVolumeClaim:
          claimName: kavya-models-pvc
      - name: data-storage
        persistentVolumeClaim:
          claimName: kavya-data-pvc
      - name: logs-storage
        persistentVolumeClaim:
          claimName: kavya-logs-pvc
      - name: config-volume
        configMap:
          name: kavya-config
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "kavya.io/dedicated"
        operator: "Equal"
        value: "backend"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - kavya-backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-backend-service
  namespace: kavya-amr
  labels:
    app: kavya-backend
    component: api
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: kavya-backend
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kavya-backend-sa
  namespace: kavya-amr
  labels:
    app: kavya-backend
    component: api
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: kavya-amr
  name: kavya-backend-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kavya-backend-rolebinding
  namespace: kavya-amr
subjects:
- kind: ServiceAccount
  name: kavya-backend-sa
  namespace: kavya-amr
roleRef:
  kind: Role
  name: kavya-backend-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: kavya-backend-hpa
  namespace: kavya-amr
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: kavya-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: kavya-backend-pdb
  namespace: kavya-amr
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: kavya-backend
