"""
KAVYA AMR System - Security Module
Authentication, authorization, and security utilities
"""

import os
import jwt
import bcrypt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import Crypt<PERSON>ontext
import secrets
import hashlib
from cryptography.fernet import <PERSON>rnet

from .config import get_settings

settings = get_settings()
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Initialize encryption
if settings.ENCRYPTION_KEY:
    fernet = Fernet(settings.ENCRYPTION_KEY.encode())
else:
    fernet = Fernet(Fernet.generate_key())


class SecurityManager:
    """Centralized security management"""
    
    def __init__(self):
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_minutes = settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def generate_api_key(self) -> str:
        """Generate secure API key"""
        return secrets.token_urlsafe(32)
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        return fernet.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return fernet.decrypt(encrypted_data.encode()).decode()
    
    def hash_data(self, data: str) -> str:
        """Create SHA-256 hash of data"""
        return hashlib.sha256(data.encode()).hexdigest()


# Global security manager instance
security_manager = SecurityManager()


def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Dependency to verify JWT token"""
    return security_manager.verify_token(credentials.credentials)


def get_current_user(token_data: Dict[str, Any] = Depends(verify_token)) -> Dict[str, Any]:
    """Get current authenticated user"""
    user_id = token_data.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # In production, fetch user from database
    # For now, return mock user data
    return {
        "user_id": user_id,
        "username": token_data.get("username", "unknown"),
        "email": token_data.get("email", "<EMAIL>"),
        "roles": token_data.get("roles", ["user"]),
        "permissions": token_data.get("permissions", []),
        "institution": token_data.get("institution", "Unknown"),
        "is_active": True,
        "is_verified": True
    }


def require_permission(permission: str):
    """Decorator to require specific permission"""
    def permission_checker(current_user: Dict[str, Any] = Depends(get_current_user)):
        if permission not in current_user.get("permissions", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    return permission_checker


def require_role(role: str):
    """Decorator to require specific role"""
    def role_checker(current_user: Dict[str, Any] = Depends(get_current_user)):
        if role not in current_user.get("roles", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{role}' required"
            )
        return current_user
    return role_checker


class RateLimiter:
    """Simple in-memory rate limiter"""
    
    def __init__(self):
        self.requests = {}
    
    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """Check if request is allowed based on rate limit"""
        now = datetime.utcnow()
        
        if key not in self.requests:
            self.requests[key] = []
        
        # Remove old requests outside the window
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if (now - req_time).total_seconds() < window
        ]
        
        # Check if under limit
        if len(self.requests[key]) < limit:
            self.requests[key].append(now)
            return True
        
        return False


# Global rate limiter instance
rate_limiter = RateLimiter()


def rate_limit(limit: int = 100, window: int = 60):
    """Rate limiting decorator"""
    def rate_limit_checker(request):
        # In production, use client IP or user ID
        key = "global"  # Simplified for demo
        
        if not rate_limiter.is_allowed(key, limit, window):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded"
            )
        return True
    return rate_limit_checker


class AuditLogger:
    """Security audit logging"""
    
    def __init__(self):
        self.log_file = "logs/security_audit.log"
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
    
    def log_event(self, event_type: str, user_id: str, details: Dict[str, Any]):
        """Log security event"""
        timestamp = datetime.utcnow().isoformat()
        log_entry = {
            "timestamp": timestamp,
            "event_type": event_type,
            "user_id": user_id,
            "details": details
        }
        
        # In production, use proper logging framework
        with open(self.log_file, "a") as f:
            f.write(f"{log_entry}\n")
    
    def log_login(self, user_id: str, success: bool, ip_address: str):
        """Log login attempt"""
        self.log_event("login", user_id, {
            "success": success,
            "ip_address": ip_address
        })
    
    def log_api_access(self, user_id: str, endpoint: str, method: str):
        """Log API access"""
        self.log_event("api_access", user_id, {
            "endpoint": endpoint,
            "method": method
        })
    
    def log_data_access(self, user_id: str, resource_type: str, resource_id: str):
        """Log data access"""
        self.log_event("data_access", user_id, {
            "resource_type": resource_type,
            "resource_id": resource_id
        })


# Global audit logger instance
audit_logger = AuditLogger()


def anonymize_patient_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Anonymize patient data for privacy protection"""
    anonymized = data.copy()
    
    # Remove direct identifiers
    sensitive_fields = [
        "name", "first_name", "last_name", "full_name",
        "ssn", "social_security_number", "mrn", "medical_record_number",
        "phone", "phone_number", "email", "address",
        "date_of_birth", "dob", "birth_date"
    ]
    
    for field in sensitive_fields:
        if field in anonymized:
            del anonymized[field]
    
    # Hash patient ID for consistency
    if "patient_id" in anonymized:
        anonymized["patient_id"] = security_manager.hash_data(anonymized["patient_id"])
    
    # Add anonymization metadata
    anonymized["_anonymized"] = True
    anonymized["_anonymization_timestamp"] = datetime.utcnow().isoformat()
    
    return anonymized


def validate_hipaa_compliance(data: Dict[str, Any]) -> bool:
    """Validate HIPAA compliance of data"""
    # Check for PHI (Protected Health Information)
    phi_indicators = [
        "name", "ssn", "phone", "email", "address",
        "date_of_birth", "medical_record_number"
    ]
    
    for indicator in phi_indicators:
        if indicator in data:
            return False
    
    return True


def generate_consent_token(patient_id: str, purpose: str, expiry_days: int = 365) -> str:
    """Generate consent token for data usage"""
    data = {
        "patient_id": patient_id,
        "purpose": purpose,
        "granted_at": datetime.utcnow().isoformat(),
        "expires_at": (datetime.utcnow() + timedelta(days=expiry_days)).isoformat()
    }
    
    return security_manager.create_access_token(data)


def verify_consent(token: str, purpose: str) -> bool:
    """Verify consent token for specific purpose"""
    try:
        data = security_manager.verify_token(token)
        return data.get("purpose") == purpose
    except:
        return False


class DataEncryption:
    """Data encryption utilities"""
    
    @staticmethod
    def encrypt_field(value: str) -> str:
        """Encrypt a single field"""
        return security_manager.encrypt_data(value)
    
    @staticmethod
    def decrypt_field(encrypted_value: str) -> str:
        """Decrypt a single field"""
        return security_manager.decrypt_data(encrypted_value)
    
    @staticmethod
    def encrypt_record(record: Dict[str, Any], fields_to_encrypt: list) -> Dict[str, Any]:
        """Encrypt specific fields in a record"""
        encrypted_record = record.copy()
        
        for field in fields_to_encrypt:
            if field in encrypted_record:
                encrypted_record[field] = DataEncryption.encrypt_field(str(encrypted_record[field]))
                encrypted_record[f"{field}_encrypted"] = True
        
        return encrypted_record
    
    @staticmethod
    def decrypt_record(record: Dict[str, Any], fields_to_decrypt: list) -> Dict[str, Any]:
        """Decrypt specific fields in a record"""
        decrypted_record = record.copy()
        
        for field in fields_to_decrypt:
            if field in decrypted_record and decrypted_record.get(f"{field}_encrypted"):
                decrypted_record[field] = DataEncryption.decrypt_field(decrypted_record[field])
                del decrypted_record[f"{field}_encrypted"]
        
        return decrypted_record


# Security middleware functions
def add_security_headers(response):
    """Add security headers to response"""
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    return response


def validate_input_sanitization(data: Any) -> bool:
    """Validate input for potential security threats"""
    if isinstance(data, str):
        # Check for SQL injection patterns
        sql_patterns = ["'", "--", "/*", "*/", "xp_", "sp_", "DROP", "DELETE", "INSERT", "UPDATE"]
        for pattern in sql_patterns:
            if pattern.lower() in data.lower():
                return False
        
        # Check for XSS patterns
        xss_patterns = ["<script", "javascript:", "onload=", "onerror=", "onclick="]
        for pattern in xss_patterns:
            if pattern.lower() in data.lower():
                return False
    
    return True
