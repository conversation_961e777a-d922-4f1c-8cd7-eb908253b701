<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KAVYA AMR System - Complete Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .pulse-animation { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .loading { display: none; }
        .loading.active { display: inline-block; }
    </style>
</head>
<body class="bg-gray-50" x-data="kavyaApp()">
    <!-- Navigation -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-dna text-purple-600 text-xl"></i>
                        </div>
                        <h1 class="text-white text-2xl font-bold">KAVYA AMR</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-white">
                        <i class="fas fa-heartbeat mr-2"></i>
                        <span x-text="systemStatus"></span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        
        <!-- Hero Section -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                🧬 AI-Powered Antimicrobial Resistance System
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Complete diagnostic analysis, nanobot therapy, and global surveillance in one integrated platform
            </p>
        </div>

        <!-- System Status Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-server text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">System Status</p>
                        <p class="text-2xl font-semibold text-gray-900" x-text="metrics.status">Healthy</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-microscope text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Analyses</p>
                        <p class="text-2xl font-semibold text-gray-900" x-text="metrics.analyses">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-robot text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Nanobots</p>
                        <p class="text-2xl font-semibold text-gray-900" x-text="metrics.nanobots">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                        <i class="fas fa-globe text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Surveillance</p>
                        <p class="text-2xl font-semibold text-gray-900" x-text="metrics.surveillance">Active</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Features Tabs -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button @click="activeTab = 'diagnostic'" 
                            :class="activeTab === 'diagnostic' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="fas fa-microscope mr-2"></i>Diagnostic Analysis
                    </button>
                    <button @click="activeTab = 'nanobot'" 
                            :class="activeTab === 'nanobot' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="fas fa-robot mr-2"></i>Nanobot Therapy
                    </button>
                    <button @click="activeTab = 'surveillance'" 
                            :class="activeTab === 'surveillance' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="fas fa-globe mr-2"></i>Global Surveillance
                    </button>
                    <button @click="activeTab = 'quantum'" 
                            :class="activeTab === 'quantum' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <i class="fas fa-atom mr-2"></i>Quantum Analysis
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6">
                
                <!-- Diagnostic Analysis Tab -->
                <div x-show="activeTab === 'diagnostic'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">AI-Powered Diagnostic Analysis</h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Input Form -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Patient ID</label>
                                <input type="text" x-model="diagnosticForm.patientId" 
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="Enter patient ID">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Sample Type</label>
                                <select x-model="diagnosticForm.sampleType" 
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500">
                                    <option value="blood">Blood</option>
                                    <option value="urine">Urine</option>
                                    <option value="swab">Swab</option>
                                    <option value="sputum">Sputum</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Clinical Data</label>
                                <textarea x-model="diagnosticForm.clinicalData" 
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                          rows="3" placeholder="Enter clinical observations..."></textarea>
                            </div>
                            
                            <button @click="runDiagnosticAnalysis()" 
                                    :disabled="loading.diagnostic"
                                    class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50">
                                <i class="fas fa-play mr-2"></i>
                                <span x-show="!loading.diagnostic">Run Analysis</span>
                                <span x-show="loading.diagnostic" class="flex items-center justify-center">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Analyzing...
                                </span>
                            </button>
                        </div>
                        
                        <!-- Results -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Analysis Results</h4>
                            <div x-show="diagnosticResults" class="space-y-3">
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Pathogen Identified:</p>
                                    <p class="text-lg text-purple-600" x-text="diagnosticResults?.pathogen_identification?.primary_pathogen"></p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Confidence Score:</p>
                                    <p class="text-lg text-green-600" x-text="(diagnosticResults?.confidence_score * 100)?.toFixed(1) + '%'"></p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Recommended Treatment:</p>
                                    <p class="text-lg text-blue-600" x-text="diagnosticResults?.treatment_recommendations?.primary_therapy"></p>
                                </div>
                            </div>
                            <div x-show="!diagnosticResults" class="text-gray-500 text-center py-8">
                                <i class="fas fa-microscope text-4xl mb-4"></i>
                                <p>Run an analysis to see results</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Nanobot Therapy Tab -->
                <div x-show="activeTab === 'nanobot'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Nanobot Therapy Deployment</h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Deployment Form -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Patient ID</label>
                                <input type="text" x-model="nanobotForm.patientId" 
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="Enter patient ID">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Target Pathogen</label>
                                <input type="text" x-model="nanobotForm.targetPathogen" 
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="e.g., Staphylococcus aureus">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Nanobot Count</label>
                                <input type="number" x-model="nanobotForm.nanobotCount" 
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="1000000" min="1000" max="10000000">
                            </div>
                            
                            <button @click="deployNanobots()" 
                                    :disabled="loading.nanobot"
                                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50">
                                <i class="fas fa-rocket mr-2"></i>
                                <span x-show="!loading.nanobot">Deploy Nanobots</span>
                                <span x-show="loading.nanobot" class="flex items-center justify-center">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Deploying...
                                </span>
                            </button>
                        </div>
                        
                        <!-- Deployment Status -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Deployment Status</h4>
                            <div x-show="nanobotResults" class="space-y-3">
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Deployment ID:</p>
                                    <p class="text-sm text-gray-600 font-mono" x-text="nanobotResults?.deployment_id"></p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Status:</p>
                                    <p class="text-lg text-blue-600 capitalize" x-text="nanobotResults?.status"></p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Safety Score:</p>
                                    <p class="text-lg text-green-600" x-text="(nanobotResults?.safety_score * 100)?.toFixed(1) + '%'"></p>
                                </div>
                            </div>
                            <div x-show="!nanobotResults" class="text-gray-500 text-center py-8">
                                <i class="fas fa-robot text-4xl mb-4"></i>
                                <p>Deploy nanobots to see status</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Global Surveillance Tab -->
                <div x-show="activeTab === 'surveillance'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Global AMR Surveillance</h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h4 class="font-medium text-red-800">High Risk Regions</h4>
                            <ul class="mt-2 text-sm text-red-700">
                                <li>• Southeast Asia</li>
                                <li>• Eastern Europe</li>
                                <li>• Sub-Saharan Africa</li>
                            </ul>
                        </div>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h4 class="font-medium text-yellow-800">Emerging Threats</h4>
                            <ul class="mt-2 text-sm text-yellow-700">
                                <li>• Carbapenem-resistant Enterobacteriaceae</li>
                                <li>• Multidrug-resistant TB</li>
                                <li>• Antifungal-resistant Candida</li>
                            </ul>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-medium text-green-800">Success Stories</h4>
                            <ul class="mt-2 text-sm text-green-700">
                                <li>• MRSA reduction in hospitals</li>
                                <li>• Improved stewardship programs</li>
                                <li>• Enhanced surveillance networks</li>
                            </ul>
                        </div>
                    </div>
                    
                    <button @click="loadSurveillanceData()" 
                            class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh Global Data
                    </button>
                </div>

                <!-- Quantum Analysis Tab -->
                <div x-show="activeTab === 'quantum'" class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">Quantum Protein Analysis</h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Input -->
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Protein Sequence</label>
                                <textarea x-model="quantumForm.proteinSequence" 
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                          rows="4" placeholder="Enter protein sequence...">MKLLNVINFVFLMFVSSSKILGYGQF</textarea>
                            </div>
                            
                            <button @click="runQuantumAnalysis()" 
                                    :disabled="loading.quantum"
                                    class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 disabled:opacity-50">
                                <i class="fas fa-atom mr-2"></i>
                                <span x-show="!loading.quantum">Run Quantum Analysis</span>
                                <span x-show="loading.quantum" class="flex items-center justify-center">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Computing...
                                </span>
                            </button>
                        </div>
                        
                        <!-- Results -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Quantum Results</h4>
                            <div x-show="quantumResults" class="space-y-3">
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Folding Energy:</p>
                                    <p class="text-lg text-indigo-600" x-text="quantumResults?.folding_energy + ' kJ/mol'"></p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Stability Score:</p>
                                    <p class="text-lg text-green-600" x-text="(quantumResults?.stability_score * 100)?.toFixed(1) + '%'"></p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <p class="text-sm font-medium text-gray-700">Quantum Advantage:</p>
                                    <p class="text-lg text-purple-600" x-text="quantumResults?.quantum_advantage + 'x speedup'"></p>
                                </div>
                            </div>
                            <div x-show="!quantumResults" class="text-gray-500 text-center py-8">
                                <i class="fas fa-atom text-4xl mb-4"></i>
                                <p>Run quantum analysis to see results</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function kavyaApp() {
            return {
                activeTab: 'diagnostic',
                systemStatus: 'Healthy',
                metrics: {
                    status: 'Healthy',
                    analyses: 0,
                    nanobots: 0,
                    surveillance: 'Active'
                },
                loading: {
                    diagnostic: false,
                    nanobot: false,
                    quantum: false
                },
                diagnosticForm: {
                    patientId: '',
                    sampleType: 'blood',
                    clinicalData: ''
                },
                nanobotForm: {
                    patientId: '',
                    targetPathogen: '',
                    nanobotCount: 1000000
                },
                quantumForm: {
                    proteinSequence: 'MKLLNVINFVFLMFVSSSKILGYGQF'
                },
                diagnosticResults: null,
                nanobotResults: null,
                quantumResults: null,

                async init() {
                    await this.checkSystemHealth();
                    setInterval(() => this.checkSystemHealth(), 30000);
                },

                async checkSystemHealth() {
                    try {
                        const response = await fetch('http://localhost:8001/health');
                        const data = await response.json();
                        this.systemStatus = data.status === 'healthy' ? 'Healthy' : 'Warning';
                        this.metrics.analyses = data.metrics?.total_analyses || 0;
                    } catch (error) {
                        this.systemStatus = 'Offline';
                    }
                },

                async runDiagnosticAnalysis() {
                    this.loading.diagnostic = true;
                    try {
                        const response = await fetch('http://localhost:8001/api/v1/diagnostic/analyze', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                patient_id: this.diagnosticForm.patientId || 'demo_patient',
                                sample_type: this.diagnosticForm.sampleType,
                                clinical_data: { notes: this.diagnosticForm.clinicalData }
                            })
                        });
                        this.diagnosticResults = await response.json();
                        this.metrics.analyses++;
                    } catch (error) {
                        alert('Analysis failed: ' + error.message);
                    }
                    this.loading.diagnostic = false;
                },

                async deployNanobots() {
                    this.loading.nanobot = true;
                    try {
                        const response = await fetch('http://localhost:8001/api/v1/nanobot/deploy', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                patient_id: this.nanobotForm.patientId || 'demo_patient',
                                target_pathogen: this.nanobotForm.targetPathogen || 'Staphylococcus aureus',
                                target_site: { x: 10, y: 5, z: 3 },
                                nanobot_count: parseInt(this.nanobotForm.nanobotCount)
                            })
                        });
                        this.nanobotResults = await response.json();
                        this.metrics.nanobots++;
                    } catch (error) {
                        alert('Deployment failed: ' + error.message);
                    }
                    this.loading.nanobot = false;
                },

                async runQuantumAnalysis() {
                    this.loading.quantum = true;
                    try {
                        const response = await fetch(`http://localhost:8001/api/v1/quantum/simulate?protein_sequence=${encodeURIComponent(this.quantumForm.proteinSequence)}`);
                        this.quantumResults = await response.json();
                    } catch (error) {
                        alert('Quantum analysis failed: ' + error.message);
                    }
                    this.loading.quantum = false;
                },

                async loadSurveillanceData() {
                    // Simulate loading surveillance data
                    alert('Global surveillance data refreshed successfully!');
                }
            }
        }
    </script>
</body>
</html>
