{"version": 3, "sources": ["../../src/lib/redirect-status.ts"], "names": ["RedirectStatusCode", "allowedStatusCodes", "Set", "getRedirectStatus", "route", "statusCode", "permanent", "PermanentRedirect", "TemporaryRedirect", "modifyRouteRegex", "regex", "restrictedPaths", "replace", "map", "path", "join"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,4CAA2C;AAE9E,OAAO,MAAMC,qBAAqB,IAAIC,IAAI;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI,EAAC;AAEpE,OAAO,SAASC,kBAAkBC,KAGjC;IACC,OACEA,MAAMC,UAAU,IACfD,CAAAA,MAAME,SAAS,GACZN,mBAAmBO,iBAAiB,GACpCP,mBAAmBQ,iBAAiB,AAAD;AAE3C;AAEA,+DAA+D;AAC/D,0DAA0D;AAC1D,gDAAgD;AAChD,OAAO,SAASC,iBAAiBC,KAAa,EAAEC,eAA0B;IACxE,IAAIA,iBAAiB;QACnBD,QAAQA,MAAME,OAAO,CACnB,MACA,CAAC,IAAI,EAAED,gBACJE,GAAG,CAAC,CAACC,OAASA,KAAKF,OAAO,CAAC,OAAO,QAClCG,IAAI,CAAC,KAAK,CAAC,CAAC;IAEnB;IACAL,QAAQA,MAAME,OAAO,CAAC,OAAO;IAC7B,OAAOF;AACT"}