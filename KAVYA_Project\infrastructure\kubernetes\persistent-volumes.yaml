apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
  labels:
    app: kavya-storage
    component: storage-class
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Retain
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: standard-hdd
  labels:
    app: kavya-storage
    component: storage-class
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp2
  encrypted: "true"
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kavya-models-pvc
  namespace: kavya-amr
  labels:
    app: kavya-storage
    component: ai-models
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kavya-data-pvc
  namespace: kavya-amr
  labels:
    app: kavya-storage
    component: data
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 500Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kavya-logs-pvc
  namespace: kavya-amr
  labels:
    app: kavya-storage
    component: logs
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: standard-hdd
  resources:
    requests:
      storage: 50Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kavya-prometheus-pvc
  namespace: kavya-amr
  labels:
    app: kavya-storage
    component: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kavya-grafana-pvc
  namespace: kavya-amr
  labels:
    app: kavya-storage
    component: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kavya-redis-pvc
  namespace: kavya-amr
  labels:
    app: kavya-storage
    component: cache
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 20Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kavya-postgres-config
  namespace: kavya-amr
  labels:
    app: kavya-postgres
    component: database
data:
  postgresql.conf: |
    # PostgreSQL Configuration for KAVYA AMR System
    
    # Connection Settings
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    
    # Memory Settings
    shared_buffers = 256MB
    effective_cache_size = 1GB
    work_mem = 4MB
    maintenance_work_mem = 64MB
    
    # WAL Settings
    wal_level = replica
    max_wal_senders = 3
    max_replication_slots = 3
    wal_keep_segments = 32
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'pg_log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
    
    # Performance
    checkpoint_completion_target = 0.9
    random_page_cost = 1.1
    effective_io_concurrency = 200
    
    # Security
    ssl = on
    ssl_cert_file = 'server.crt'
    ssl_key_file = 'server.key'
    
    # Replication
    hot_standby = on
    hot_standby_feedback = on
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kavya-redis-config
  namespace: kavya-amr
  labels:
    app: kavya-redis
    component: cache
data:
  redis.conf: |
    # Redis Configuration for KAVYA AMR System
    
    # Network
    bind 0.0.0.0
    port 6379
    timeout 300
    tcp-keepalive 60
    
    # General
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    databases 16
    
    # Snapshotting
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir /data
    
    # Replication
    replica-serve-stale-data yes
    replica-read-only yes
    repl-diskless-sync no
    repl-diskless-sync-delay 5
    
    # Security
    requirepass ${REDIS_PASSWORD}
    
    # Memory Management
    maxmemory 1gb
    maxmemory-policy allkeys-lru
    
    # Append Only File
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    
    # Slow Log
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    
    # Client Output Buffer Limits
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: kavya-db-backup
  namespace: kavya-amr
  labels:
    app: kavya-backup
    component: database
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            command:
            - /bin/bash
            - -c
            - |
              TIMESTAMP=$(date +%Y%m%d_%H%M%S)
              BACKUP_FILE="kavya_backup_${TIMESTAMP}.sql"
              
              # Create backup
              pg_dump -h kavya-postgres-service -U $POSTGRES_USER -d $POSTGRES_DB > /backup/${BACKUP_FILE}
              
              # Compress backup
              gzip /backup/${BACKUP_FILE}
              
              # Upload to S3 (if configured)
              if [ ! -z "$AWS_S3_BUCKET" ]; then
                aws s3 cp /backup/${BACKUP_FILE}.gz s3://${AWS_S3_BUCKET}/backups/postgres/
              fi
              
              # Cleanup old backups (keep last 30 days)
              find /backup -name "kavya_backup_*.sql.gz" -mtime +30 -delete
            env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: kavya-db-secrets
                  key: postgres-user
            - name: POSTGRES_DB
              value: "kavya_amr"
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: kavya-db-secrets
                  key: postgres-password
            - name: AWS_S3_BUCKET
              value: "kavya-amr-backups"
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: kavya-backup-pvc
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kavya-backup-pvc
  namespace: kavya-amr
  labels:
    app: kavya-storage
    component: backup
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard-hdd
  resources:
    requests:
      storage: 200Gi
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: kavya-model-backup
  namespace: kavya-amr
  labels:
    app: kavya-backup
    component: models
spec:
  schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: model-backup
            image: amazon/aws-cli:latest
            command:
            - /bin/bash
            - -c
            - |
              TIMESTAMP=$(date +%Y%m%d_%H%M%S)
              
              # Create tar archive of models
              tar -czf /backup/models_backup_${TIMESTAMP}.tar.gz -C /models .
              
              # Upload to S3
              if [ ! -z "$AWS_S3_BUCKET" ]; then
                aws s3 cp /backup/models_backup_${TIMESTAMP}.tar.gz s3://${AWS_S3_BUCKET}/backups/models/
              fi
              
              # Cleanup old backups (keep last 4 weeks)
              find /backup -name "models_backup_*.tar.gz" -mtime +28 -delete
            env:
            - name: AWS_S3_BUCKET
              value: "kavya-amr-backups"
            volumeMounts:
            - name: models-storage
              mountPath: /models
              readOnly: true
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: models-storage
            persistentVolumeClaim:
              claimName: kavya-models-pvc
          - name: backup-storage
            persistentVolumeClaim:
              claimName: kavya-backup-pvc
          restartPolicy: OnFailure
