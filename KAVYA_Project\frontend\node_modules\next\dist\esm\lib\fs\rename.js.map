{"version": 3, "sources": ["../../../src/lib/fs/rename.ts"], "names": ["fs", "promisify", "rename", "source", "target", "windowsRetryTimeout", "process", "platform", "renameWithRetry", "Date", "now", "startTime", "retryTimeout", "attempt", "error", "code", "console", "abortRetry", "stat", "isFile", "e", "timeout", "Math", "min", "millis", "Promise", "resolve", "setTimeout"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,kIAAkI;AAClI,yCAAyC;AAEzC,YAAYA,QAAQ,cAAa;AACjC,SAASC,SAAS,QAAQ,OAAM;AAEhC;;;;CAIC,GACD,OAAO,eAAeC,OACpBC,MAAc,EACdC,MAAc,EACdC,sBAAsC,MAAM,uBAAuB,GAAxB;IAE3C,IAAIF,WAAWC,QAAQ;QACrB,QAAO,gEAAgE;IACzE;IAEA,IAAIE,QAAQC,QAAQ,KAAK,WAAW,OAAOF,wBAAwB,UAAU;QAC3E,6DAA6D;QAC7D,+DAA+D;QAC/D,4DAA4D;QAC5D,qEAAqE;QACrE,MAAMG,gBAAgBL,QAAQC,QAAQK,KAAKC,GAAG,IAAIL;IACpD,OAAO;QACL,MAAMJ,UAAUD,GAAGE,MAAM,EAAEC,QAAQC;IACrC;AACF;AAEA,eAAeI,gBACbL,MAAc,EACdC,MAAc,EACdO,SAAiB,EACjBC,YAAoB,EACpBC,UAAU,CAAC;IAEX,IAAI;QACF,OAAO,MAAMZ,UAAUD,GAAGE,MAAM,EAAEC,QAAQC;IAC5C,EAAE,OAAOU,OAAY;QACnB,IACEA,MAAMC,IAAI,KAAK,YACfD,MAAMC,IAAI,KAAK,WACfD,MAAMC,IAAI,KAAK,SACf;YACA,MAAMD,MAAM,yCAAyC;;QACvD;QAEA,IAAIL,KAAKC,GAAG,KAAKC,aAAaC,cAAc;YAC1CI,QAAQF,KAAK,CACX,CAAC,iCAAiC,EAAED,QAAQ,qBAAqB,EAAEC,MAAM,CAAC;YAG5E,MAAMA,MAAM,qCAAqC;;QACnD;QAEA,IAAID,YAAY,GAAG;YACjB,IAAII,aAAa;YACjB,IAAI;gBACF,MAAMC,OAAO,MAAMjB,UAAUD,GAAGkB,IAAI,EAAEd;gBACtC,IAAI,CAACc,KAAKC,MAAM,IAAI;oBAClBF,aAAa,KAAK,wFAAwF;;gBAC5G;YACF,EAAE,OAAOG,GAAG;YACV,SAAS;YACX;YAEA,IAAIH,YAAY;gBACd,MAAMH;YACR;QACF;QAEA,6CAA6C;QAC7C,MAAMO,QAAQC,KAAKC,GAAG,CAAC,KAAKV,UAAU;QAEtC,gBAAgB;QAChB,OAAOL,gBAAgBL,QAAQC,QAAQO,WAAWC,cAAcC,UAAU;IAC5E;AACF;AAEA,MAAMQ,UAAU,CAACG,SACf,IAAIC,QAAQ,CAACC,UAAYC,WAAWD,SAASF"}