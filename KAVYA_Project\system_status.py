#!/usr/bin/env python3
"""
KAVYA AMR System - Complete System Status Check
Comprehensive status check for all system components
"""

import requests
import json
from datetime import datetime
import sys

def check_component(name, url, timeout=5):
    """Check if a component is running"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            return {
                "status": "✅ HEALTHY",
                "response_time": f"{response.elapsed.total_seconds():.3f}s",
                "details": response.json() if 'application/json' in response.headers.get('content-type', '') else "OK"
            }
        else:
            return {
                "status": f"⚠️  WARNING (HTTP {response.status_code})",
                "response_time": f"{response.elapsed.total_seconds():.3f}s",
                "details": "Non-200 response"
            }
    except requests.exceptions.ConnectionError:
        return {
            "status": "❌ OFFLINE",
            "response_time": "N/A",
            "details": "Connection refused"
        }
    except requests.exceptions.Timeout:
        return {
            "status": "⏱️  TIMEOUT",
            "response_time": f">{timeout}s",
            "details": "Request timed out"
        }
    except Exception as e:
        return {
            "status": "❌ ERROR",
            "response_time": "N/A",
            "details": str(e)
        }

def print_banner():
    """Print system banner"""
    banner = """
 ██╗  ██╗ █████╗ ██╗   ██╗██╗   ██╗ █████╗ 
 ██║ ██╔╝██╔══██╗██║   ██║╚██╗ ██╔╝██╔══██╗
 █████╔╝ ███████║██║   ██║ ╚████╔╝ ███████║
 ██╔═██╗ ██╔══██║╚██╗ ██╔╝  ╚██╔╝  ██╔══██║
 ██║  ██╗██║  ██║ ╚████╔╝    ██║   ██║  ██║
 ╚═╝  ╚═╝╚═╝  ╚═╝  ╚═══╝     ╚═╝   ╚═╝  ╚═╝
                                            
 🧬 AI-Powered Antimicrobial Resistance System
 📊 Complete System Status Check
"""
    print(banner)

def main():
    """Main system status check"""
    print_banner()
    print("=" * 80)
    print(f"🕐 System Status Check - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Define all system components
    components = {
        "Simple Demo Server": "http://localhost:8000/health",
        "Full Backend API": "http://localhost:8001/health", 
        "Frontend Interface": "http://localhost:3001/",
        "Diagnostic API": "http://localhost:8001/api/v1/diagnostic/analyses",
        "Analytics Dashboard": "http://localhost:8001/api/v1/analytics/dashboard",
        "System Status API": "http://localhost:8001/api/v1/system/status"
    }
    
    print("\n🔍 Component Status:")
    print("-" * 80)
    
    all_healthy = True
    results = {}
    
    for component_name, url in components.items():
        print(f"Checking {component_name:<25} ", end="", flush=True)
        result = check_component(component_name, url)
        results[component_name] = result
        
        print(f"{result['status']:<15} ({result['response_time']})")
        
        if "❌" in result['status'] or "⚠️" in result['status']:
            all_healthy = False
    
    print("-" * 80)
    
    # Summary
    if all_healthy:
        print("\n🎉 SYSTEM STATUS: ALL COMPONENTS OPERATIONAL")
        print("✅ The KAVYA AMR system is fully functional and ready for use!")
    else:
        print("\n⚠️  SYSTEM STATUS: SOME COMPONENTS NEED ATTENTION")
        print("❌ Please check the components marked with warnings or errors.")
    
    print("\n📊 System Overview:")
    print("-" * 80)
    
    # Try to get detailed system metrics
    try:
        # Check full backend health
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"🔧 Backend Version: {health_data.get('version', 'Unknown')}")
            print(f"⏱️  System Uptime: {health_data.get('uptime_seconds', 0):.1f} seconds")
            print(f"📈 Total Analyses: {health_data.get('metrics', {}).get('total_analyses', 0)}")
            print(f"🔗 API Calls: {health_data.get('metrics', {}).get('api_calls', 0)}")
        
        # Check system status
        response = requests.get("http://localhost:8001/api/v1/system/status", timeout=5)
        if response.status_code == 200:
            status_data = response.json()
            print(f"💾 Memory Usage: {status_data.get('performance', {}).get('memory_usage', 0)*100:.1f}%")
            print(f"🖥️  CPU Usage: {status_data.get('performance', {}).get('cpu_usage', 0)*100:.1f}%")
            
    except Exception as e:
        print(f"⚠️  Could not retrieve detailed metrics: {e}")
    
    print("\n🌐 Access Points:")
    print("-" * 80)
    print("📱 Frontend Interface:     http://localhost:3001")
    print("🔧 Full Backend API:       http://localhost:8001")
    print("🧪 Simple Demo:            http://localhost:8000")
    print("📖 API Documentation:      http://localhost:8001/docs")
    print("📊 Analytics Dashboard:    http://localhost:8001/api/v1/analytics/dashboard")
    
    print("\n🚀 Available Features:")
    print("-" * 80)
    print("✅ AI-Powered Diagnostic Analysis")
    print("✅ Nanobot Therapy Simulation")
    print("✅ Global AMR Surveillance")
    print("✅ Quantum Protein Analysis")
    print("✅ Real-time System Monitoring")
    print("✅ Interactive Web Interface")
    print("✅ RESTful API with Documentation")
    print("✅ HIPAA/GDPR Compliance Ready")
    
    print("\n🧪 Quick Test Commands:")
    print("-" * 80)
    print("# Test diagnostic analysis:")
    print('curl -X POST http://localhost:8001/api/v1/diagnostic/analyze \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"patient_id": "test_001", "sample_type": "blood"}\'')
    print()
    print("# Check system health:")
    print('curl http://localhost:8001/health')
    print()
    print("# Deploy nanobots:")
    print('curl -X POST http://localhost:8001/api/v1/nanobot/deploy \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"patient_id": "test_001", "target_pathogen": "MRSA"}\'')
    
    print("\n" + "=" * 80)
    
    if all_healthy:
        print("🎯 KAVYA AMR SYSTEM: FULLY OPERATIONAL AND READY FOR PRODUCTION!")
        return 0
    else:
        print("⚠️  KAVYA AMR SYSTEM: PARTIAL OPERATION - CHECK COMPONENT STATUS")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n🛑 Status check interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Status check failed: {e}")
        sys.exit(1)
