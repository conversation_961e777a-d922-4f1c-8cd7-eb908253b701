"""
KAVYA AMR System - Genomic Resistance Prediction
DNABERT-inspired transformer model for antimicrobial resistance prediction
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModel, BertConfig
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from Bio import SeqIO
from Bio.Seq import Seq
import re
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ResistanceGene:
    """Data class for resistance gene information"""
    gene_name: str
    gene_family: str
    resistance_class: str
    mechanism: str
    confidence: float
    coordinates: Tuple[int, int]
    sequence: str


class DNATokenizer:
    """Custom DNA sequence tokenizer for genomic data"""
    
    def __init__(self, kmer_size: int = 6, stride: int = 1):
        self.kmer_size = kmer_size
        self.stride = stride
        
        # Build vocabulary
        self.vocab = self._build_vocab()
        self.vocab_size = len(self.vocab)
        self.token_to_id = {token: idx for idx, token in enumerate(self.vocab)}
        self.id_to_token = {idx: token for token, idx in self.token_to_id.items()}
        
        # Special tokens
        self.pad_token = '[PAD]'
        self.unk_token = '[UNK]'
        self.cls_token = '[CLS]'
        self.sep_token = '[SEP]'
        self.mask_token = '[MASK]'
        
    def _build_vocab(self) -> List[str]:
        """Build k-mer vocabulary"""
        bases = ['A', 'T', 'G', 'C']
        vocab = ['[PAD]', '[UNK]', '[CLS]', '[SEP]', '[MASK]']
        
        # Generate all possible k-mers
        def generate_kmers(k):
            if k == 1:
                return bases
            else:
                prev_kmers = generate_kmers(k - 1)
                return [kmer + base for kmer in prev_kmers for base in bases]
        
        kmers = generate_kmers(self.kmer_size)
        vocab.extend(kmers)
        
        return vocab
    
    def tokenize(self, sequence: str) -> List[str]:
        """Tokenize DNA sequence into k-mers"""
        sequence = sequence.upper().replace('N', 'A')  # Replace ambiguous bases
        
        tokens = [self.cls_token]
        
        for i in range(0, len(sequence) - self.kmer_size + 1, self.stride):
            kmer = sequence[i:i + self.kmer_size]
            if kmer in self.token_to_id:
                tokens.append(kmer)
            else:
                tokens.append(self.unk_token)
        
        tokens.append(self.sep_token)
        return tokens
    
    def encode(self, sequence: str, max_length: int = 512) -> Dict[str, torch.Tensor]:
        """Encode sequence to token IDs"""
        tokens = self.tokenize(sequence)
        
        # Truncate or pad
        if len(tokens) > max_length:
            tokens = tokens[:max_length-1] + [self.sep_token]
        else:
            tokens.extend([self.pad_token] * (max_length - len(tokens)))
        
        # Convert to IDs
        input_ids = [self.token_to_id.get(token, self.token_to_id[self.unk_token]) 
                    for token in tokens]
        
        # Create attention mask
        attention_mask = [1 if token != self.pad_token else 0 for token in tokens]
        
        return {
            'input_ids': torch.tensor(input_ids, dtype=torch.long),
            'attention_mask': torch.tensor(attention_mask, dtype=torch.long)
        }


class GenomicTransformer(nn.Module):
    """
    DNABERT-inspired transformer for resistance gene detection
    """
    
    def __init__(self,
                 vocab_size: int = 4100,
                 hidden_size: int = 768,
                 num_layers: int = 12,
                 num_heads: int = 12,
                 max_length: int = 512,
                 dropout: float = 0.1):
        super(GenomicTransformer, self).__init__()
        
        self.hidden_size = hidden_size
        self.max_length = max_length
        
        # Embedding layers
        self.token_embeddings = nn.Embedding(vocab_size, hidden_size)
        self.position_embeddings = nn.Embedding(max_length, hidden_size)
        self.layer_norm = nn.LayerNorm(hidden_size)
        self.dropout = nn.Dropout(dropout)
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size,
            nhead=num_heads,
            dim_feedforward=hidden_size * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # Resistance classification heads
        self.resistance_heads = nn.ModuleDict({
            'beta_lactam': nn.Linear(hidden_size, 2),
            'methicillin': nn.Linear(hidden_size, 2),
            'vancomycin': nn.Linear(hidden_size, 2),
            'fluoroquinolone': nn.Linear(hidden_size, 2),
            'aminoglycoside': nn.Linear(hidden_size, 2),
            'macrolide': nn.Linear(hidden_size, 2),
            'tetracycline': nn.Linear(hidden_size, 2),
            'chloramphenicol': nn.Linear(hidden_size, 2),
            'trimethoprim': nn.Linear(hidden_size, 2),
            'rifampin': nn.Linear(hidden_size, 2)
        })
        
        # Gene family classification
        self.gene_family_classifier = nn.Linear(hidden_size, 50)  # 50 gene families
        
        # Novel resistance detection
        self.novel_detector = nn.Sequential(
            nn.Linear(hidden_size, 256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        # Sequence-level features for interpretability
        self.sequence_attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=num_heads,
            batch_first=True
        )
        
    def forward(self, input_ids, attention_mask=None):
        batch_size, seq_length = input_ids.shape
        
        # Create position IDs
        position_ids = torch.arange(seq_length, device=input_ids.device).unsqueeze(0).repeat(batch_size, 1)
        
        # Embeddings
        token_embeds = self.token_embeddings(input_ids)
        position_embeds = self.position_embeddings(position_ids)
        embeddings = token_embeds + position_embeds
        embeddings = self.layer_norm(embeddings)
        embeddings = self.dropout(embeddings)
        
        # Create attention mask for transformer
        if attention_mask is not None:
            # Convert to boolean mask (True for positions to attend to)
            transformer_mask = attention_mask.bool()
            # Invert for transformer (False for positions to attend to)
            transformer_mask = ~transformer_mask
        else:
            transformer_mask = None
        
        # Transformer encoding
        encoded = self.transformer(embeddings, src_key_padding_mask=transformer_mask)
        
        # Global representation (CLS token or mean pooling)
        if attention_mask is not None:
            # Mean pooling over non-padded tokens
            mask_expanded = attention_mask.unsqueeze(-1).expand(encoded.size()).float()
            sum_embeddings = torch.sum(encoded * mask_expanded, 1)
            sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
            global_repr = sum_embeddings / sum_mask
        else:
            global_repr = encoded.mean(dim=1)
        
        # Resistance predictions
        resistance_outputs = {}
        for resistance_type, classifier in self.resistance_heads.items():
            resistance_outputs[resistance_type] = classifier(global_repr)
        
        # Gene family classification
        gene_family_logits = self.gene_family_classifier(global_repr)
        
        # Novel resistance detection
        novel_score = self.novel_detector(global_repr)
        
        # Sequence attention for interpretability
        attended_seq, attention_weights = self.sequence_attention(
            encoded, encoded, encoded, key_padding_mask=transformer_mask
        )
        
        return {
            'resistance_predictions': resistance_outputs,
            'gene_family_logits': gene_family_logits,
            'novel_resistance_score': novel_score,
            'sequence_embeddings': encoded,
            'global_representation': global_repr,
            'attention_weights': attention_weights
        }


class ResistancePredictor:
    """Main class for resistance prediction from genomic sequences"""
    
    def __init__(self,
                 model_path: str,
                 device: str = 'cuda',
                 confidence_threshold: float = 0.8):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.confidence_threshold = confidence_threshold
        
        # Initialize tokenizer
        self.tokenizer = DNATokenizer(kmer_size=6)
        
        # Load model
        self.model = self._load_model(model_path)
        self.model.eval()
        
        # Resistance gene database (should be loaded from external source)
        self.resistance_genes_db = self._load_resistance_database()
        
        # Gene family names
        self.gene_families = [
            'bla', 'aac', 'aph', 'ant', 'erm', 'mef', 'tet', 'cat',
            'dfr', 'sul', 'qnr', 'gyr', 'par', 'van', 'mec', 'pbp',
            # ... add more gene families
        ] + [f'family_{i}' for i in range(16, 51)]
        
    def _load_model(self, model_path: str) -> GenomicTransformer:
        """Load trained model from checkpoint"""
        try:
            model = GenomicTransformer(vocab_size=self.tokenizer.vocab_size)
            checkpoint = torch.load(model_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device)
            logger.info(f"Genomic model loaded successfully from {model_path}")
            return model
        except Exception as e:
            logger.error(f"Failed to load genomic model: {e}")
            # Return untrained model for demo purposes
            model = GenomicTransformer(vocab_size=self.tokenizer.vocab_size)
            model.to(self.device)
            return model
    
    def _load_resistance_database(self) -> Dict:
        """Load resistance gene database"""
        # This should load from CARD, ResFinder, etc.
        # For now, return a mock database
        return {
            'blaZ': {
                'family': 'beta_lactamase',
                'mechanism': 'hydrolysis',
                'resistance_class': 'beta_lactam'
            },
            'mecA': {
                'family': 'pbp',
                'mechanism': 'target_alteration',
                'resistance_class': 'methicillin'
            },
            'vanA': {
                'family': 'van',
                'mechanism': 'target_alteration',
                'resistance_class': 'vancomycin'
            }
            # ... add more genes
        }
    
    def predict_resistance(self, sequence: str) -> Dict:
        """
        Predict antimicrobial resistance from genomic sequence
        
        Args:
            sequence: DNA sequence string
            
        Returns:
            Dictionary containing resistance predictions
        """
        try:
            # Tokenize sequence
            encoded = self.tokenizer.encode(sequence, max_length=512)
            input_ids = encoded['input_ids'].unsqueeze(0).to(self.device)
            attention_mask = encoded['attention_mask'].unsqueeze(0).to(self.device)
            
            # Model inference
            with torch.no_grad():
                outputs = self.model(input_ids, attention_mask)
            
            # Process resistance predictions
            resistance_results = {}
            for resistance_type, logits in outputs['resistance_predictions'].items():
                probs = F.softmax(logits, dim=1)
                prediction = torch.argmax(probs, dim=1).item()
                confidence = torch.max(probs, dim=1).values.item()
                
                resistance_results[resistance_type] = {
                    'resistant': bool(prediction),
                    'confidence': confidence,
                    'probability': probs[0][1].item()  # Probability of resistance
                }
            
            # Gene family prediction
            gene_family_probs = F.softmax(outputs['gene_family_logits'], dim=1)
            top_families = torch.topk(gene_family_probs, 5, dim=1)
            
            # Novel resistance score
            novel_score = outputs['novel_resistance_score'].item()
            
            # Identify potential resistance genes
            detected_genes = self._identify_resistance_genes(sequence, outputs)
            
            results = {
                'resistance_profile': resistance_results,
                'gene_families': [
                    {
                        'family': self.gene_families[idx.item()],
                        'confidence': prob.item()
                    }
                    for idx, prob in zip(top_families.indices[0], top_families.values[0])
                ],
                'novel_resistance_probability': novel_score,
                'detected_genes': detected_genes,
                'overall_confidence': np.mean([r['confidence'] for r in resistance_results.values()]),
                'high_confidence_prediction': all(
                    r['confidence'] > self.confidence_threshold 
                    for r in resistance_results.values()
                )
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Resistance prediction failed: {e}")
            raise
    
    def _identify_resistance_genes(self, sequence: str, model_outputs: Dict) -> List[ResistanceGene]:
        """
        Identify specific resistance genes in the sequence
        
        Args:
            sequence: Input DNA sequence
            model_outputs: Model prediction outputs
            
        Returns:
            List of detected resistance genes
        """
        detected_genes = []
        
        # Use attention weights to identify important regions
        attention_weights = model_outputs['attention_weights']
        
        # Simple gene detection based on known patterns
        # In practice, this would use more sophisticated methods like BLAST
        for gene_name, gene_info in self.resistance_genes_db.items():
            # Mock gene detection - replace with actual gene finding algorithm
            if gene_name.lower() in sequence.lower():
                gene = ResistanceGene(
                    gene_name=gene_name,
                    gene_family=gene_info['family'],
                    resistance_class=gene_info['resistance_class'],
                    mechanism=gene_info['mechanism'],
                    confidence=0.9,  # Mock confidence
                    coordinates=(0, len(gene_name)),  # Mock coordinates
                    sequence=gene_name  # Mock sequence
                )
                detected_genes.append(gene)
        
        return detected_genes
    
    def analyze_genome(self, genome_file: str) -> Dict:
        """
        Analyze complete genome for resistance genes
        
        Args:
            genome_file: Path to genome FASTA file
            
        Returns:
            Comprehensive resistance analysis
        """
        try:
            # Read genome sequences
            sequences = []
            for record in SeqIO.parse(genome_file, "fasta"):
                sequences.append(str(record.seq))
            
            # Combine all sequences
            full_genome = "".join(sequences)
            
            # Split into overlapping windows for analysis
            window_size = 2000
            overlap = 200
            results = []
            
            for i in range(0, len(full_genome) - window_size + 1, window_size - overlap):
                window_seq = full_genome[i:i + window_size]
                window_result = self.predict_resistance(window_seq)
                window_result['window_start'] = i
                window_result['window_end'] = i + window_size
                results.append(window_result)
            
            # Aggregate results
            aggregated_results = self._aggregate_window_results(results)
            
            return aggregated_results
            
        except Exception as e:
            logger.error(f"Genome analysis failed: {e}")
            raise
    
    def _aggregate_window_results(self, window_results: List[Dict]) -> Dict:
        """Aggregate results from multiple genome windows"""
        # Aggregate resistance predictions
        resistance_aggregated = {}
        resistance_types = window_results[0]['resistance_profile'].keys()
        
        for resistance_type in resistance_types:
            resistant_count = sum(
                1 for result in window_results 
                if result['resistance_profile'][resistance_type]['resistant']
            )
            avg_confidence = np.mean([
                result['resistance_profile'][resistance_type]['confidence']
                for result in window_results
            ])
            
            resistance_aggregated[resistance_type] = {
                'resistant': resistant_count > len(window_results) * 0.3,  # 30% threshold
                'confidence': avg_confidence,
                'supporting_windows': resistant_count
            }
        
        # Aggregate detected genes
        all_genes = []
        for result in window_results:
            all_genes.extend(result['detected_genes'])
        
        # Remove duplicates and merge overlapping detections
        unique_genes = self._merge_gene_detections(all_genes)
        
        return {
            'resistance_profile': resistance_aggregated,
            'detected_genes': unique_genes,
            'total_windows_analyzed': len(window_results),
            'novel_resistance_regions': [
                result for result in window_results
                if result['novel_resistance_probability'] > 0.8
            ]
        }
    
    def _merge_gene_detections(self, genes: List[ResistanceGene]) -> List[ResistanceGene]:
        """Merge overlapping gene detections"""
        # Simple deduplication by gene name
        unique_genes = {}
        for gene in genes:
            if gene.gene_name not in unique_genes:
                unique_genes[gene.gene_name] = gene
            else:
                # Keep the one with higher confidence
                if gene.confidence > unique_genes[gene.gene_name].confidence:
                    unique_genes[gene.gene_name] = gene
        
        return list(unique_genes.values())
