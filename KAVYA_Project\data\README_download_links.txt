# 📊 KAVYA Dataset Download Links & Data Sources

## 🎯 **Overview**

This document provides comprehensive links and instructions for downloading the datasets required for the KAVYA AMR system. All datasets are organized by category and include licensing information, access requirements, and preprocessing instructions.

## 🦠 **Pathogen Identification Datasets**

### **DIBaS - Digital Images of Bacterial Species**
- **URL**: https://www.kaggle.com/datasets/maysee/dibas-digital-images-of-bacterial-species
- **Size**: ~2.5 GB
- **Format**: JPEG images (1024x768)
- **Classes**: 33 bacterial species
- **License**: CC BY 4.0
- **Access**: Public, Kaggle account required
- **Download Command**:
  ```bash
  kaggle datasets download -d maysee/dibas-digital-images-of-bacterial-species
  ```

### **Gram Stain Classification Dataset**
- **URL**: https://www.kaggle.com/datasets/vuppalaadithyasairam/gramstain-classification-dataset
- **Size**: ~1.2 GB
- **Format**: PNG images (256x256)
- **Classes**: Gram-positive, Gram-negative, Gram-variable
- **License**: CC0 1.0
- **Access**: Public
- **Download Command**:
  ```bash
  kaggle datasets download -d vuppalaadithyasairam/gramstain-classification-dataset
  ```

### **Bacterial Colony Morphology Dataset**
- **URL**: https://zenodo.org/record/4737294
- **Size**: ~3.8 GB
- **Format**: TIFF images (2048x2048)
- **Classes**: 15 morphology types + antibiotic susceptibility
- **License**: CC BY 4.0
- **Access**: Public, Zenodo account recommended
- **DOI**: 10.5281/zenodo.4737294

### **VGG Bacterial Dataset (Pre-training)**
- **URL**: https://www.robots.ox.ac.uk/~vgg/data/bacteria/
- **Size**: ~5.2 GB
- **Format**: JPEG images (various sizes)
- **Classes**: 50+ bacterial species
- **License**: Academic use only
- **Access**: Registration required

## 🧬 **Genomic & Resistance Datasets**

### **CARD - Comprehensive Antibiotic Resistance Database**
- **URL**: https://card.mcmaster.ca/download
- **Size**: ~500 MB (compressed)
- **Format**: FASTA, JSON, TSV
- **Content**: 6,000+ resistance genes, 3,000+ mutations
- **License**: Open Data Commons Open Database License
- **Access**: Public
- **Download Command**:
  ```bash
  wget https://card.mcmaster.ca/latest/data
  tar -xzf data
  ```

### **NDARO - NIH National Database of Antibiotic Resistant Organisms**
- **URL**: https://www.ncbi.nlm.nih.gov/pathogens/antimicrobial-resistance/
- **Size**: ~2.1 TB (full dataset)
- **Format**: FASTA, GenBank, SRA
- **Content**: 500,000+ bacterial genomes with resistance data
- **License**: Public domain
- **Access**: Public, NCBI account recommended
- **Download via SRA Toolkit**:
  ```bash
  prefetch --max-size 50G SRR_ACCESSION_LIST
  fasterq-dump --split-files SRR_ACCESSION
  ```

### **PATRIC/BV-BRC Bacterial Genomes**
- **URL**: https://www.bv-brc.org/
- **Size**: ~10 TB (selective download recommended)
- **Format**: FASTA, GFF, GenBank
- **Content**: 300,000+ bacterial genomes
- **License**: Public domain
- **Access**: Public, registration required for bulk downloads
- **API Access**:
  ```bash
  curl -X GET "https://www.bv-brc.org/api/genome/?eq(genome_status,Complete)&limit(1000)"
  ```

### **ResFinder Database**
- **URL**: https://bitbucket.org/genomicepidemiology/resfinder_db/src/master/
- **Size**: ~50 MB
- **Format**: FASTA
- **Content**: Curated resistance gene sequences
- **License**: Apache 2.0
- **Access**: Public
- **Download Command**:
  ```bash
  git clone https://bitbucket.org/genomicepidemiology/resfinder_db.git
  ```

### **MG-RAST Metagenomic Data**
- **URL**: https://www.mg-rast.org/
- **Size**: Variable (project-specific)
- **Format**: FASTA, FASTQ, Biom
- **Content**: Metagenomic datasets with resistance annotations
- **License**: Varies by project
- **Access**: Registration required
- **API Documentation**: https://github.com/MG-RAST/MG-RAST-Tools

### **ENA/SRA SSTI Pathogen Sequences**
- **URL**: https://www.ebi.ac.uk/ena/browser/
- **Search Query**: "skin soft tissue infection" AND "whole genome sequencing"
- **Size**: ~5 TB (estimated)
- **Format**: FASTQ, FASTA
- **Content**: Clinical isolate sequences
- **License**: Varies by study
- **Access**: Public
- **Download Example**:
  ```bash
  enaDataGet -f fastq -d . PRJEB12345
  ```

## 🏥 **Clinical & Biomarker Datasets**

### **MIMIC-IV Clinical Database**
- **URL**: https://physionet.org/content/mimiciv/
- **Size**: ~6 GB (compressed)
- **Format**: CSV, SQL dumps
- **Content**: ICU patient data with infection outcomes
- **License**: PhysioNet Credentialed Health Data License
- **Access**: Credentialed access required (CITI training)
- **Application**: https://physionet.org/register/

### **eICU Collaborative Research Database**
- **URL**: https://physionet.org/content/eicu-crd/
- **Size**: ~3.2 GB
- **Format**: CSV
- **Content**: Multi-center ICU data
- **License**: PhysioNet Credentialed Health Data License
- **Access**: Credentialed access required

### **ImmPort Immunology Database**
- **URL**: https://www.immport.org/shared/search
- **Size**: Variable by study
- **Format**: TSV, XML
- **Content**: Immune response data and biomarkers
- **License**: Varies by study
- **Access**: Registration required
- **Search Terms**: "antimicrobial", "infection", "cytokine"

### **BioGPS Gene Expression Atlas**
- **URL**: http://biogps.org/
- **Size**: ~2 GB (relevant subsets)
- **Format**: CEL, TXT
- **Content**: Gene expression in immune responses
- **License**: Academic use
- **Access**: Public

### **CytokineDB**
- **URL**: http://www.cytokinedb.org/
- **Size**: ~100 MB
- **Format**: XML, JSON
- **Content**: Cytokine interaction networks
- **License**: Academic use
- **Access**: Public

## 🤖 **Nanomedicine & Therapy Datasets**

### **PhagesDB - Bacteriophage Database**
- **URL**: https://phagesdb.org/
- **Size**: ~1 GB
- **Format**: FASTA, GenBank
- **Content**: 15,000+ phage genomes
- **License**: CC BY 4.0
- **Access**: Public
- **Download**: Bulk download available via FTP

### **INPHARED - Phage Database**
- **URL**: https://github.com/RyanCook94/inphared
- **Size**: ~500 MB
- **Format**: FASTA, TSV
- **Content**: Curated phage genomes with host information
- **License**: MIT
- **Access**: Public
- **Download Command**:
  ```bash
  git clone https://github.com/RyanCook94/inphared.git
  ```

### **DRAMP - Antimicrobial Peptide Database**
- **URL**: http://dramp.cpu-bioinfor.org/
- **Size**: ~50 MB
- **Format**: FASTA, CSV
- **Content**: 22,000+ antimicrobial peptides
- **License**: Academic use
- **Access**: Public, registration recommended

### **APD3 - Antimicrobial Peptide Database**
- **URL**: https://aps.unmc.edu/
- **Size**: ~25 MB
- **Format**: FASTA, XML
- **Content**: 3,000+ peptides with activity data
- **License**: Academic use
- **Access**: Public

### **Biofilm Resistance Data (JBEI)**
- **URL**: https://public-jbei.lbl.gov/
- **Size**: ~200 MB
- **Format**: CSV, JSON
- **Content**: Biofilm formation and resistance data
- **License**: Open source
- **Access**: Public

## 🌍 **Global Surveillance Datasets**

### **GLASS - WHO Global AMR Surveillance**
- **URL**: https://www.who.int/glass/resources/publications/
- **Size**: ~100 MB (annual reports)
- **Format**: PDF, Excel
- **Content**: Global resistance surveillance data
- **License**: WHO open access
- **Access**: Public

### **ReAct - Global AMR Data**
- **URL**: https://www.reactgroup.org/
- **Size**: ~50 MB
- **Format**: Excel, PDF
- **Content**: AMR policy and surveillance data
- **License**: Open access
- **Access**: Public

### **ECDC AMR Surveillance Reports**
- **URL**: https://www.ecdc.europa.eu/en/antimicrobial-resistance/surveillance-and-disease-data
- **Size**: ~200 MB (historical data)
- **Format**: Excel, CSV
- **Content**: European AMR surveillance
- **License**: ECDC copyright
- **Access**: Public

### **OpenEpi Outbreak Data**
- **URL**: https://www.openepi.com/
- **Size**: Variable
- **Format**: CSV, JSON
- **Content**: Epidemiological outbreak data
- **License**: Open source
- **Access**: Public

### **GIDEON Global Infectious Disease Database**
- **URL**: https://www.gideononline.com/
- **Size**: ~1 GB (subscription data)
- **Format**: Proprietary
- **Content**: Global infectious disease surveillance
- **License**: Commercial license required
- **Access**: Subscription required

## 📋 **Data Preprocessing Instructions**

### **Image Data Preprocessing**
```python
# Standard preprocessing pipeline
import cv2
import numpy as np
from sklearn.preprocessing import StandardScaler

def preprocess_microscopy_image(image_path):
    # Load and resize
    img = cv2.imread(image_path)
    img = cv2.resize(img, (224, 224))

    # Normalize
    img = img.astype(np.float32) / 255.0

    # Color space conversion if needed
    img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

    return img, img_hsv
```

### **Genomic Data Preprocessing**
```bash
# Quality control and assembly pipeline
fastqc raw_reads/*.fastq.gz
trimmomatic PE -threads 8 input_R1.fastq.gz input_R2.fastq.gz \
    output_R1_paired.fastq.gz output_R1_unpaired.fastq.gz \
    output_R2_paired.fastq.gz output_R2_unpaired.fastq.gz \
    ILLUMINACLIP:adapters.fa:2:30:10 LEADING:3 TRAILING:3 SLIDINGWINDOW:4:15 MINLEN:36

# Assembly
spades.py --pe1-1 output_R1_paired.fastq.gz --pe1-2 output_R2_paired.fastq.gz \
    -o assembly_output --threads 8
```

### **Clinical Data Preprocessing**
```python
# Biomarker data standardization
import pandas as pd
from sklearn.preprocessing import StandardScaler, LabelEncoder

def preprocess_clinical_data(df):
    # Handle missing values
    df = df.fillna(df.median(numeric_only=True))

    # Encode categorical variables
    le = LabelEncoder()
    categorical_cols = df.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        df[col] = le.fit_transform(df[col].astype(str))

    # Standardize numerical features
    scaler = StandardScaler()
    numerical_cols = df.select_dtypes(include=[np.number]).columns
    df[numerical_cols] = scaler.fit_transform(df[numerical_cols])

    return df
```

## 🔐 **Data Access & Licensing**

### **Public Datasets**
- No registration required
- Direct download available
- Attribution required for publications

### **Credentialed Access**
- CITI training required for clinical datasets
- IRB approval may be needed for research use
- Data use agreements required

### **Commercial Datasets**
- Subscription or license fees apply
- Academic discounts often available
- Contact vendors for pricing

### **Restricted Datasets**
- Application process required
- Specific use case justification needed
- May require institutional approval

## 📞 **Support & Contact**

### **Data Access Issues**
- **Email**: <EMAIL>
- **Documentation**: https://docs.kavya-amr.org/data
- **Community Forum**: https://forum.kavya-amr.org

### **Dataset Contributions**
- **Email**: <EMAIL>
- **Guidelines**: https://docs.kavya-amr.org/contribute
- **Quality Standards**: https://docs.kavya-amr.org/standards

---

**Note**: Dataset availability and access requirements may change. Always verify current access procedures and licensing terms before downloading. For the most up-to-date information, visit the official KAVYA documentation at https://docs.kavya-amr.org