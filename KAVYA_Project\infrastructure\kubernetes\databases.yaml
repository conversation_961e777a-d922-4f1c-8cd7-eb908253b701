apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kavya-postgres
  namespace: kavya-amr
  labels:
    app: kavya-postgres
    component: database
spec:
  serviceName: kavya-postgres-headless
  replicas: 3
  selector:
    matchLabels:
      app: kavya-postgres
  template:
    metadata:
      labels:
        app: kavya-postgres
        component: database
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          value: "kavya_amr"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: kavya-db-secrets
              key: postgres-user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kavya-db-secrets
              key: postgres-password
        - name: POSTGRES_REPLICATION_USER
          valueFrom:
            secretKeyRef:
              name: kavya-db-secrets
              key: postgres-replication-user
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kavya-db-secrets
              key: postgres-replication-password
        - name: PGDATA
          value: "/var/lib/postgresql/data/pgdata"
        resources:
          requests:
            cpu: 500m
            memory: 2Gi
          limits:
            cpu: 2
            memory: 8Gi
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
      volumes:
      - name: postgres-config
        configMap:
          name: kavya-postgres-config
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-postgres-service
  namespace: kavya-amr
  labels:
    app: kavya-postgres
    component: database
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: kavya-postgres
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-postgres-headless
  namespace: kavya-amr
  labels:
    app: kavya-postgres
    component: database
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: kavya-postgres
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kavya-mongodb
  namespace: kavya-amr
  labels:
    app: kavya-mongodb
    component: genomics-database
spec:
  serviceName: kavya-mongodb-headless
  replicas: 3
  selector:
    matchLabels:
      app: kavya-mongodb
  template:
    metadata:
      labels:
        app: kavya-mongodb
        component: genomics-database
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: mongodb
        image: mongo:6.0
        ports:
        - containerPort: 27017
          name: mongodb
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: kavya-db-secrets
              key: mongodb-root-user
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kavya-db-secrets
              key: mongodb-root-password
        - name: MONGO_INITDB_DATABASE
          value: "kavya_genomics"
        command:
        - mongod
        - --replSet
        - rs0
        - --bind_ip_all
        - --auth
        resources:
          requests:
            cpu: 500m
            memory: 2Gi
          limits:
            cpu: 2
            memory: 8Gi
        volumeMounts:
        - name: mongodb-storage
          mountPath: /data/db
        - name: mongodb-config
          mountPath: /data/configdb
        livenessProbe:
          exec:
            command:
            - mongo
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        readinessProbe:
          exec:
            command:
            - mongo
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
  volumeClaimTemplates:
  - metadata:
      name: mongodb-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 200Gi
  - metadata:
      name: mongodb-config
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-mongodb-service
  namespace: kavya-amr
  labels:
    app: kavya-mongodb
    component: genomics-database
spec:
  type: ClusterIP
  ports:
  - port: 27017
    targetPort: 27017
    protocol: TCP
    name: mongodb
  selector:
    app: kavya-mongodb
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-mongodb-headless
  namespace: kavya-amr
  labels:
    app: kavya-mongodb
    component: genomics-database
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 27017
    targetPort: 27017
    protocol: TCP
    name: mongodb
  selector:
    app: kavya-mongodb
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kavya-redis
  namespace: kavya-amr
  labels:
    app: kavya-redis
    component: cache
spec:
  replicas: 3
  selector:
    matchLabels:
      app: kavya-redis
  template:
    metadata:
      labels:
        app: kavya-redis
        component: cache
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - /etc/redis/redis.conf
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kavya-db-secrets
              key: redis-password
        resources:
          requests:
            cpu: 100m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 2Gi
        volumeMounts:
        - name: redis-config
          mountPath: /etc/redis
        - name: redis-storage
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: redis-config
        configMap:
          name: kavya-redis-config
      - name: redis-storage
        persistentVolumeClaim:
          claimName: kavya-redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: kavya-redis-service
  namespace: kavya-amr
  labels:
    app: kavya-redis
    component: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: kavya-redis
