{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts"], "names": ["restoreReducer", "state", "action", "url", "tree", "href", "createHrefFromUrl", "treeToRestore", "<PERSON><PERSON><PERSON>", "cache", "newCache", "process", "env", "__NEXT_PPR", "updateCacheNodeOnPopstateRestoration", "extractPathFromFlightRouterState", "buildId", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "prefetchCache", "nextUrl", "pathname"], "mappings": ";;;;+BASgBA;;;eAAAA;;;mCATkB;oCAMe;gCACI;AAE9C,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGF;IACtB,MAAMG,OAAOC,IAAAA,oCAAiB,EAACH;IAC/B,0EAA0E;IAC1E,4FAA4F;IAC5F,gGAAgG;IAChG,6FAA6F;IAC7F,8DAA8D;IAC9D,yGAAyG;IACzG,MAAMI,gBAAgBH,QAAQH,MAAMG,IAAI;IAExC,MAAMI,WAAWP,MAAMQ,KAAK;IAC5B,MAAMC,WAAWC,QAAQC,GAAG,CAACC,UAAU,GAEnC,qEAAqE;IACrE,2DAA2D;IAC3D,2BAA2B;IAC3BC,IAAAA,oDAAoC,EAACN,UAAUD,iBAC/CC;QAiBOO;IAfX,OAAO;QACLC,SAASf,MAAMe,OAAO;QACtB,oBAAoB;QACpBC,cAAcZ;QACda,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,6FAA6F;YAC7FC,4BAA4B;QAC9B;QACAC,mBAAmBrB,MAAMqB,iBAAiB;QAC1Cb,OAAOC;QACPa,eAAetB,MAAMsB,aAAa;QAClC,wBAAwB;QACxBnB,MAAMG;QACNiB,SAAST,CAAAA,oCAAAA,IAAAA,oDAAgC,EAACR,0BAAjCQ,oCAAmDZ,IAAIsB,QAAQ;IAC1E;AACF"}